<?php
/**
 * Smart WebP Conversion Global AJAX Handlers
 *
 * Contains global AJAX handlers and initialization functions
 * that work with the Enhanced WebP class only.
 *
 * @package Redco_Optimizer
 * @subpackage Smart_WebP_Conversion
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Global stats AJAX handler (works regardless of module status)
function redco_webp_ajax_get_stats() {
    // CRITICAL FIX: More flexible nonce verification
    $nonce_verified = false;
    if (isset($_POST['nonce'])) {
        $nonce_verified = wp_verify_nonce($_POST['nonce'], 'redco_webp_stats') ||
                         wp_verify_nonce($_POST['nonce'], 'redco_optimizer_nonce');
    }

    if (!$nonce_verified) {
        wp_send_json_error('Invalid nonce');
        return;
    }

    if (!current_user_can('manage_options')) {
        wp_send_json_error('Insufficient permissions');
        return;
    }

    // Check if module is enabled
    if (!redco_is_module_enabled('smart-webp-conversion')) {
        // Return default stats when module is disabled
        wp_send_json(array(
            'total_images' => 0,
            'converted_images' => 0,
            'unconverted_images' => 0,
            'conversion_percentage' => 0,
            'total_original_size' => 0,
            'total_webp_size' => 0,
            'total_savings' => 0,
            'savings_percentage' => 0,
            'recent_conversions' => array(),
            'server_support' => function_exists('imagewebp') && (imagetypes() & IMG_WEBP),
            'browser_support' => false
        ));
        return;
    }

    // Get WebP module instance
    global $redco_webp_instance;
    if (!$redco_webp_instance) {
        if (class_exists('Redco_Smart_WebP_Conversion')) {
            $redco_webp_instance = new Redco_Smart_WebP_Conversion();
        } else {
            wp_send_json_error('WebP conversion class not available');
            return;
        }
    }

    // Call the instance method
    wp_send_json($redco_webp_instance->get_stats());
}

// Global test conversion AJAX handler (works regardless of module status)
function redco_webp_ajax_test_conversion() {
    if (!wp_verify_nonce($_POST['nonce'], 'redco_webp_test') && !wp_verify_nonce($_POST['nonce'], 'redco_optimizer_nonce')) {
        wp_send_json_error('Invalid nonce');
        return;
    }

    if (!current_user_can('manage_options')) {
        wp_send_json_error('Insufficient permissions');
        return;
    }

    $result = array(
        'success' => false,
        'message' => '',
        'capabilities' => array()
    );

    // Test WebP support
    $webp_support = function_exists('imagewebp') && (imagetypes() & IMG_WEBP);
    $result['capabilities']['webp_support'] = $webp_support;
    $result['capabilities']['gd_version'] = function_exists('gd_info') ? gd_info()['GD Version'] : 'Not available';
    $result['capabilities']['supported_formats'] = array();

    if (function_exists('imagetypes')) {
        $types = imagetypes();
        $result['capabilities']['supported_formats']['jpeg'] = (bool)($types & IMG_JPG);
        $result['capabilities']['supported_formats']['png'] = (bool)($types & IMG_PNG);
        $result['capabilities']['supported_formats']['gif'] = (bool)($types & IMG_GIF);
        $result['capabilities']['supported_formats']['webp'] = (bool)($types & IMG_WEBP);
    }

    if ($webp_support) {
        $result['success'] = true;
        $result['message'] = __('Server supports WebP conversion', 'redco-optimizer');
    } else {
        $result['message'] = __('Server does not support WebP conversion. Please contact your hosting provider.', 'redco-optimizer');
    }

    wp_send_json($result);
}

// Global bulk conversion AJAX handler
function redco_webp_ajax_bulk_convert() {
    // Security check
    if (!current_user_can('manage_options')) {
        wp_send_json_error('Insufficient permissions');
        return;
    }

    // Check if module is enabled
    if (!redco_is_module_enabled('smart-webp-conversion')) {
        wp_send_json_error('Module not enabled');
        return;
    }

    // Get WebP module instance
    global $redco_webp_instance;
    if (!$redco_webp_instance) {
        if (class_exists('Redco_Smart_WebP_Conversion')) {
            $redco_webp_instance = new Redco_Smart_WebP_Conversion();
        } else {
            wp_send_json_error('WebP conversion class not available');
            return;
        }
    }

    // Call the WebP class method
    $redco_webp_instance->ajax_bulk_convert();
}

// Initialize Smart WebP Conversion module
function redco_init_smart_webp_conversion() {
    if (redco_is_module_enabled('smart-webp-conversion')) {
        global $redco_webp_instance;

        // Load the WebP conversion class
        if (!class_exists('Redco_Smart_WebP_Conversion')) {
            require_once REDCO_OPTIMIZER_PLUGIN_DIR . 'modules/smart-webp-conversion/class-smart-webp-conversion.php';
        }

        // Initialize WebP class
        try {
            $redco_webp_instance = new Redco_Smart_WebP_Conversion();
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('✅ WebP conversion class initialized successfully');
            }
        } catch (Exception $e) {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('❌ Failed to initialize WebP conversion class: ' . $e->getMessage());
            }
        } catch (Error $e) {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('❌ Fatal error initializing WebP conversion class: ' . $e->getMessage());
            }
        }

        // CRITICAL FIX: Enqueue WebP admin scripts and localize
        if (is_admin()) {
            add_action('admin_enqueue_scripts', 'redco_webp_enqueue_admin_scripts');
        }
    }
}

// CRITICAL FIX: Enqueue WebP admin scripts with proper localization
function redco_webp_enqueue_admin_scripts($hook) {
    // CRITICAL DEBUG: Log the hook name to see what we're getting
    if (defined('WP_DEBUG') && WP_DEBUG) {
        error_log('🔧 WebP Script Enqueue: Hook = ' . $hook);
    }

    // FIXED: More flexible hook checking for plugin pages
    $plugin_pages = array(
        'redco-optimizer',
        'redco_optimizer',
        'toplevel_page_redco-optimizer',
        'redco-optimizer_page_redco-optimizer-modules'
    );

    $is_plugin_page = false;
    foreach ($plugin_pages as $page) {
        if (strpos($hook, $page) !== false) {
            $is_plugin_page = true;
            break;
        }
    }

    if (!$is_plugin_page) {
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('🔧 WebP Script Enqueue: Not a plugin page, skipping');
        }
        return;
    }

    if (defined('WP_DEBUG') && WP_DEBUG) {
        error_log('🔧 WebP Script Enqueue: Enqueuing scripts for hook ' . $hook);
    }

    // Enqueue WebP admin JavaScript
    wp_enqueue_script(
        'redco-webp-admin',
        REDCO_OPTIMIZER_PLUGIN_URL . 'modules/smart-webp-conversion/assets/js/admin.js',
        array('jquery'),
        REDCO_OPTIMIZER_VERSION,
        true
    );

    // CRITICAL FIX: Localize script with proper nonces and AJAX URL
    wp_localize_script('redco-webp-admin', 'redcoWebP', array(
        'ajaxurl' => admin_url('admin-ajax.php'),
        'nonces' => array(
            'bulk_convert' => wp_create_nonce('redco_webp_bulk_convert'),
            'stats' => wp_create_nonce('redco_webp_stats'),
            'test' => wp_create_nonce('redco_webp_test')
        ),
        'strings' => array(
            'converting' => __('Converting...', 'redco-optimizer'),
            'completed' => __('Conversion completed!', 'redco-optimizer'),
            'error' => __('Conversion failed', 'redco-optimizer'),
            'noImages' => __('No images to convert', 'redco-optimizer')
        )
    ));

    if (defined('WP_DEBUG') && WP_DEBUG) {
        error_log('✅ WebP Script Enqueue: Scripts and localization completed');
    }
}

// Only register the init hook if it hasn't been registered already
if (!has_action('init', 'redco_init_smart_webp_conversion')) {
    add_action('init', 'redco_init_smart_webp_conversion', 10);
}

// AJAX handler for getting convertible images count
function redco_webp_ajax_get_convertible_count() {
    // Security check
    if (!current_user_can('manage_options')) {
        wp_send_json_error('Insufficient permissions');
        return;
    }

    // Check if module is enabled
    if (!redco_is_module_enabled('smart-webp-conversion')) {
        wp_send_json_success(array(
            'convertible_count' => 0,
            'has_convertible_images' => false,
            'message' => 'Module not enabled'
        ));
        return;
    }

    try {
        // Get WebP instance
        if (!class_exists('Redco_Smart_WebP_Conversion')) {
            require_once REDCO_OPTIMIZER_PLUGIN_DIR . 'modules/smart-webp-conversion/class-smart-webp-conversion.php';
        }

        $webp_instance = new Redco_Smart_WebP_Conversion();
        $convertible_count = $webp_instance->get_convertible_images_count();

        $response_data = array(
            'convertible_count' => $convertible_count,
            'has_convertible_images' => $convertible_count > 0,
            'message' => $convertible_count > 0 ?
                sprintf('%d images ready for conversion', $convertible_count) :
                'No images available for conversion'
        );

        wp_send_json_success($response_data);

    } catch (Exception $e) {
        wp_send_json_error('Failed to get convertible images count: ' . $e->getMessage());
    }
}

// AJAX handler for getting processable images (CRITICAL FIX: Missing handler)
function redco_webp_ajax_get_processable_images() {
    // ENHANCED DEBUG: Log the request
    if (defined('WP_DEBUG') && WP_DEBUG) {
        error_log('🔧 AJAX Handler: redco_webp_get_processable_images called');
        error_log('🔧 POST data: ' . print_r($_POST, true));
    }

    // Security check
    if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'redco_webp_bulk_convert')) {
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('❌ AJAX Handler: Nonce verification failed');
        }
        wp_send_json_error(array('message' => 'Security check failed - invalid nonce'));
        return;
    }

    if (!current_user_can('manage_options')) {
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('❌ AJAX Handler: User lacks manage_options capability');
        }
        wp_send_json_error(array('message' => 'Security check failed - insufficient permissions'));
        return;
    }

    // Check if module is enabled
    if (!redco_is_module_enabled('smart-webp-conversion')) {
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('❌ AJAX Handler: WebP module not enabled');
        }
        wp_send_json_error(array('message' => 'Module not enabled'));
        return;
    }

    try {
        // Get WebP instance
        if (!class_exists('Redco_Smart_WebP_Conversion')) {
            $class_file = REDCO_OPTIMIZER_PLUGIN_DIR . 'modules/smart-webp-conversion/class-smart-webp-conversion.php';
            if (!file_exists($class_file)) {
                throw new Exception('WebP class file not found: ' . $class_file);
            }
            require_once $class_file;
        }

        if (!class_exists('Redco_Smart_WebP_Conversion')) {
            throw new Exception('WebP class could not be loaded');
        }

        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('✅ AJAX Handler: WebP class loaded successfully');
        }

        $webp_instance = new Redco_Smart_WebP_Conversion();

        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('✅ AJAX Handler: WebP instance created');
        }

        // Check if method exists
        if (!method_exists($webp_instance, 'get_all_processable_images')) {
            throw new Exception('Method get_all_processable_images does not exist');
        }

        $processable_images = $webp_instance->get_all_processable_images();

        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('✅ AJAX Handler: Found ' . count($processable_images) . ' processable images');
        }

        wp_send_json_success(array(
            'total_images' => count($processable_images),
            'image_ids' => array_column($processable_images, 'ID'),
            'message' => 'Found ' . count($processable_images) . ' processable images'
        ));

    } catch (Exception $e) {
        $error_message = 'Failed to get processable images: ' . $e->getMessage();
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('❌ AJAX Handler Exception: ' . $error_message);
            error_log('❌ Exception trace: ' . $e->getTraceAsString());
        }
        wp_send_json_error(array(
            'message' => $error_message,
            'debug_info' => array(
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
            )
        ));
    } catch (Error $e) {
        $error_message = 'Fatal error getting processable images: ' . $e->getMessage();
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('❌ AJAX Handler Fatal Error: ' . $error_message);
            error_log('❌ Fatal error trace: ' . $e->getTraceAsString());
        }
        wp_send_json_error(array(
            'message' => $error_message,
            'debug_info' => array(
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
            )
        ));
    }
}

// AJAX handler for getting recent conversions
function redco_webp_ajax_get_recent_conversions() {
    // CRITICAL FIX: More flexible nonce verification for recent conversions
    $nonce_verified = false;
    if (isset($_POST['nonce'])) {
        $nonce_verified = wp_verify_nonce($_POST['nonce'], 'redco_webp_stats') ||
                         wp_verify_nonce($_POST['nonce'], 'redco_optimizer_nonce');
    }

    if (!$nonce_verified) {
        wp_send_json_error('Invalid nonce');
        return;
    }

    // Security check
    if (!current_user_can('manage_options')) {
        wp_send_json_error('Insufficient permissions');
        return;
    }

    // Check if module is enabled
    if (!redco_is_module_enabled('smart-webp-conversion')) {
        wp_send_json_success(array(
            'conversions' => array(),
            'total_count' => 0,
            'has_more' => false
        ));
        return;
    }

    try {
        $limit = isset($_POST['limit']) ? intval($_POST['limit']) : 10;
        $offset = isset($_POST['offset']) ? intval($_POST['offset']) : 0;

        // Get WebP instance
        if (!class_exists('Redco_Smart_WebP_Conversion')) {
            require_once REDCO_OPTIMIZER_PLUGIN_DIR . 'modules/smart-webp-conversion/class-smart-webp-conversion.php';
        }

        $webp_instance = new Redco_Smart_WebP_Conversion();
        $conversions = $webp_instance->get_recent_conversions($limit, $offset);
        $total_count = $webp_instance->get_conversions_count();

        wp_send_json_success(array(
            'conversions' => $conversions,
            'total_count' => $total_count,
            'has_more' => ($offset + $limit) < $total_count,
            'current_offset' => $offset,
            'current_limit' => $limit
        ));

    } catch (Exception $e) {
        wp_send_json_error('Failed to get recent conversions: ' . $e->getMessage());
    }
}

// AJAX handler for refreshing stats
function redco_webp_ajax_refresh_stats() {
    // Security check
    if (!current_user_can('manage_options')) {
        wp_send_json_error('Insufficient permissions');
        return;
    }

    // Check if module is enabled
    if (!redco_is_module_enabled('smart-webp-conversion')) {
        wp_send_json_error('Module not enabled');
        return;
    }

    try {
        // Get WebP instance
        if (!class_exists('Redco_Smart_WebP_Conversion')) {
            $class_file = REDCO_OPTIMIZER_PLUGIN_DIR . 'modules/smart-webp-conversion/class-smart-webp-conversion.php';
            if (!file_exists($class_file)) {
                throw new Exception('WebP class file not found: ' . $class_file);
            }
            require_once $class_file;
        }

        if (!class_exists('Redco_Smart_WebP_Conversion')) {
            throw new Exception('WebP class could not be loaded');
        }

        $webp_instance = new Redco_Smart_WebP_Conversion();

        // Force refresh stats by clearing any cached data
        if (method_exists($webp_instance, 'clear_cached_stats')) {
            $webp_instance->clear_cached_stats();
        }

        // Check if get_stats method exists
        if (!method_exists($webp_instance, 'get_stats')) {
            throw new Exception('Method get_stats does not exist in WebP class');
        }

        // Get fresh stats
        $stats = $webp_instance->get_stats();

        // Validate stats data
        if (!is_array($stats)) {
            throw new Exception('Invalid stats data returned: ' . gettype($stats));
        }

        wp_send_json_success(array(
            'stats' => $stats,
            'message' => 'Statistics refreshed successfully',
            'timestamp' => current_time('mysql'),
            'debug_info' => array(
                'total_images' => isset($stats['total_images']) ? $stats['total_images'] : 'N/A',
                'converted_images' => isset($stats['converted_images']) ? $stats['converted_images'] : 'N/A',
                'unconverted_images' => isset($stats['unconverted_images']) ? $stats['unconverted_images'] : 'N/A'
            )
        ));

    } catch (Exception $e) {
        wp_send_json_error('Failed to refresh stats: ' . $e->getMessage());
    } catch (Error $e) {
        wp_send_json_error('Fatal error refreshing stats: ' . $e->getMessage());
    }
}

// AJAX handler for scanning images
function redco_webp_ajax_scan_images() {
    // Security check
    if (!current_user_can('manage_options')) {
        wp_send_json_error('Insufficient permissions');
        return;
    }

    // Check if module is enabled
    if (!redco_is_module_enabled('smart-webp-conversion')) {
        wp_send_json_error('Module not enabled');
        return;
    }

    try {
        global $wpdb;

        // Get all image attachments
        $all_images = $wpdb->get_results("
            SELECT p.ID, p.post_title, p.post_mime_type
            FROM {$wpdb->posts} p
            WHERE p.post_type = 'attachment'
            AND p.post_mime_type IN ('image/jpeg', 'image/jpg', 'image/png', 'image/gif')
            ORDER BY p.ID DESC
        ");

        // Get converted images
        $converted_images = $wpdb->get_results("
            SELECT DISTINCT pm.post_id
            FROM {$wpdb->postmeta} pm
            INNER JOIN {$wpdb->posts} p ON pm.post_id = p.ID
            WHERE pm.meta_key = '_webp_conversion_data'
            AND pm.meta_value LIKE '%\"converted\":true%'
            AND p.post_type = 'attachment'
            AND p.post_mime_type IN ('image/jpeg', 'image/jpg', 'image/png', 'image/gif')
        ");

        $converted_ids = array_column($converted_images, 'post_id');
        $unconverted_count = 0;
        $total_size = 0;
        $scan_results = array();

        // Analyze each image
        foreach ($all_images as $image) {
            $file_path = redco_safe_get_attached_file($image->ID);
            $is_converted = in_array($image->ID, $converted_ids);

            if ($file_path && file_exists($file_path)) {
                $file_size = filesize($file_path);
                $total_size += $file_size;

                if (!$is_converted) {
                    $unconverted_count++;
                }

                $scan_results[] = array(
                    'id' => $image->ID,
                    'title' => $image->post_title,
                    'mime_type' => $image->post_mime_type,
                    'file_size' => $file_size,
                    'is_converted' => $is_converted,
                    'file_exists' => true
                );
            }
        }

        // Calculate potential savings
        $estimated_savings = $unconverted_count * 0.3; // 30% average savings
        $estimated_size_savings = $total_size * 0.3;

        wp_send_json_success(array(
            'total_images' => count($all_images),
            'converted_images' => count($converted_images),
            'unconverted_images' => $unconverted_count,
            'total_size' => $total_size,
            'estimated_savings_percent' => 30,
            'estimated_size_savings' => $estimated_size_savings,
            'scan_results' => array_slice($scan_results, 0, 20), // Limit to 20 for performance
            'message' => "Scan complete: {$unconverted_count} images ready for WebP conversion"
        ));

    } catch (Exception $e) {
        wp_send_json_error('Failed to scan images: ' . $e->getMessage());
    }
}

// AJAX handler for analyzing WebP potential
function redco_webp_ajax_analyze_webp_potential() {
    // Security check
    if (!current_user_can('manage_options')) {
        wp_send_json_error('Insufficient permissions');
        return;
    }

    try {
        // Get WebP instance
        if (!class_exists('Redco_Smart_WebP_Conversion')) {
            require_once REDCO_OPTIMIZER_PLUGIN_DIR . 'modules/smart-webp-conversion/class-smart-webp-conversion.php';
        }

        $webp_instance = new Redco_Smart_WebP_Conversion();
        $processable_images = $webp_instance->get_all_processable_images();

        // Analyze file sizes and potential savings
        $total_original_size = 0;
        $analysis_details = array();

        foreach (array_slice($processable_images, 0, 10) as $image) {
            $file_path = redco_safe_get_attached_file($image->ID);
            if ($file_path && file_exists($file_path)) {
                $file_size = filesize($file_path);
                $total_original_size += $file_size;

                $analysis_details[] = array(
                    'id' => $image->ID,
                    'title' => $image->post_title,
                    'mime_type' => $image->post_mime_type,
                    'file_size' => $file_size,
                    'estimated_webp_size' => $file_size * 0.7, // 30% savings
                    'estimated_savings' => $file_size * 0.3
                );
            }
        }

        $estimated_total_savings = $total_original_size * 0.3;

        wp_send_json_success(array(
            'processable_count' => count($processable_images),
            'total_original_size' => $total_original_size,
            'estimated_total_savings' => $estimated_total_savings,
            'savings_percentage' => 30,
            'analysis_details' => $analysis_details,
            'server_support' => function_exists('imagewebp') && (imagetypes() & IMG_WEBP),
            'message' => count($processable_images) . ' images analyzed for WebP potential'
        ));

    } catch (Exception $e) {
        wp_send_json_error('Failed to analyze WebP potential: ' . $e->getMessage());
    }
}



// Register AJAX handlers
if (is_admin()) {
    add_action('wp_ajax_redco_webp_bulk_convert', 'redco_webp_ajax_bulk_convert');
    add_action('wp_ajax_redco_webp_get_stats', 'redco_webp_ajax_get_stats');
    add_action('wp_ajax_redco_webp_test_conversion', 'redco_webp_ajax_test_conversion');
    add_action('wp_ajax_redco_webp_get_convertible_count', 'redco_webp_ajax_get_convertible_count');
    add_action('wp_ajax_redco_webp_get_processable_images', 'redco_webp_ajax_get_processable_images');
    add_action('wp_ajax_redco_webp_get_recent_conversions', 'redco_webp_ajax_get_recent_conversions');

    // NEW AJAX handlers for new functionality
    add_action('wp_ajax_redco_webp_refresh_stats', 'redco_webp_ajax_refresh_stats');
    add_action('wp_ajax_redco_webp_scan_images', 'redco_webp_ajax_scan_images');
    add_action('wp_ajax_redco_webp_analyze_webp_potential', 'redco_webp_ajax_analyze_webp_potential');
}
