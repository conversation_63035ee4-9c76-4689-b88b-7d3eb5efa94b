/**
 * Smart WebP Conversion Admin JavaScript
 *
 * @package Redco_Optimizer
 * @subpackage Smart_WebP_Conversion
 * @since 1.0.0
 */

// Script loading verification
window.webpScriptLoaded = true;
console.log('🔧 WebP admin script loaded successfully');

// CRITICAL DEBUG: Check if redcoWebP object is available immediately
if (typeof redcoWebP !== 'undefined') {
    console.log('✅ redcoWebP object is available:', redcoWebP);
} else {
    console.warn('⚠️ redcoWebP object is not available yet');
}

// Define global test functions
window.testWebPInit = function() {
    if (typeof window.WebPAdmin !== 'undefined') {
        window.WebPAdmin.init();
    }
};

window.testWebPModal = function() {
    if (typeof window.WebPAdmin !== 'undefined') {
        if (!window.WebPAdmin.initialized) {
            window.WebPAdmin.init();
        }
        window.WebPAdmin.showModal();
    }
};

window.testWebPDebug = function() {
    if (typeof window.WebPAdmin !== 'undefined') {
        if (!window.WebPAdmin.initialized) {
            window.WebPAdmin.init();
        }
        window.WebPAdmin.debugImageDetection();
    }
};

// CRITICAL DEBUG: Test function to check redcoWebP availability
window.testRedcoWebP = function() {
    console.log('🔧 Testing redcoWebP object availability...');
    if (typeof redcoWebP !== 'undefined') {
        console.log('✅ redcoWebP is available:', redcoWebP);
        console.log('✅ AJAX URL:', redcoWebP.ajaxurl);
        console.log('✅ Nonces:', redcoWebP.nonces);
        return true;
    } else {
        console.error('❌ redcoWebP object is not available!');
        console.log('Available global objects:', Object.keys(window).filter(key => key.includes('redco') || key.includes('webp')));
        return false;
    }
};

(function($) {
    'use strict';

    var WebPAdmin = {

        // Properties
        isConverting: false,
        conversionData: {
            totalImages: 0,
            processedImages: 0,
            errorCount: 0,
            totalSavings: 0,
            currentBatch: 0,
            batchSize: 10
        },

        // Initialize
        init: function() {
            if (this.initialized) {
                return;
            }

            this.initProgressModal();
            this.bindEvents();
            this.updateStats();
            // REMOVED: updateConvertButtonState() - no longer needed with new button system
            this.initialized = true;
        },

        // Initialize progress modal
        initProgressModal: function() {
            const existingModal = $('#webp-progress-modal');

            // Create progress modal HTML if it doesn't exist
            if (existingModal.length === 0) {
                const modalHtml = `
                    <div id="webp-progress-modal" class="redco-modal" style="display: none;">
                        <div class="redco-modal-content compact-modal">
                            <div class="redco-modal-header">
                                <h3 id="webp-progress-modal-title">Converting Images to WebP</h3>
                            </div>
                            <div class="redco-modal-body">
                                <div class="progress-container">
                                    <div class="progress-bar">
                                        <div class="progress-fill" style="width: 0%"></div>
                                    </div>
                                    <div class="progress-stats left-aligned">
                                        <span class="progress-current">0</span> / <span class="progress-total">0</span> images processed
                                    </div>
                                </div>
                                <div class="conversion-stats single-row">
                                    <div class="stat-card">
                                        <span class="stat-label">Processed</span>
                                        <span class="stat-value processed-count">0</span>
                                    </div>
                                    <div class="stat-card">
                                        <span class="stat-label">Errors</span>
                                        <span class="stat-value error-count">0</span>
                                    </div>
                                    <div class="stat-card">
                                        <span class="stat-label">Savings</span>
                                        <span class="stat-value total-savings">0 KB</span>
                                    </div>
                                </div>
                                <div class="current-operation">
                                    <div class="current-file">Preparing conversion...</div>
                                </div>
                                <div class="conversion-log-section">
                                    <div class="log-toggle" id="log-toggle">
                                        <span class="dashicons dashicons-arrow-right-alt2"></span>
                                        <span class="log-toggle-text">Conversion Log</span>
                                        <span class="log-count">(0 entries)</span>
                                    </div>
                                    <div class="conversion-log collapsed" id="conversion-log">
                                        <div class="log-entries"></div>
                                    </div>
                                </div>
                            </div>
                            <div class="redco-modal-footer">
                                <button type="button" class="button button-secondary" id="cancel-conversion">Cancel</button>
                                <button type="button" class="button button-primary" id="close-conversion-modal" style="display: none;">Close</button>
                            </div>
                        </div>
                    </div>
                `;
                $('body').append(modalHtml);

                // Bind collapsible log toggle
                this.bindLogToggle();
            }
        },

        // Bind collapsible log toggle functionality
        bindLogToggle: function() {
            $(document).on('click', '#log-toggle', function() {
                const $toggle = $(this);
                const $log = $('#conversion-log');
                const $icon = $toggle.find('.dashicons');

                if ($log.hasClass('collapsed')) {
                    // Expand log
                    $log.removeClass('collapsed').addClass('expanded');
                    $icon.removeClass('dashicons-arrow-right-alt2').addClass('dashicons-arrow-down-alt2');
                } else {
                    // Collapse log
                    $log.removeClass('expanded').addClass('collapsed');
                    $icon.removeClass('dashicons-arrow-down-alt2').addClass('dashicons-arrow-right-alt2');
                }
            });
        },

        // Update log entry count
        updateLogCount: function() {
            const entryCount = $('.log-entries .log-entry').length;
            $('.log-count').text(`(${entryCount} entries)`);
        },

        // Bind event handlers
        bindEvents: function() {
            // UPDATED: Handle new button functionality
            $(document).on('click', '#header-refresh-stats', function(e) {
                e.preventDefault();
                console.log('🔄 Refresh Stats button clicked!');
                WebPAdmin.refreshStats();
            });

            $(document).on('click', '#scan-images', function(e) {
                e.preventDefault();
                console.log('🔍 Scan Images button clicked!');
                WebPAdmin.scanImages();
            });

            $(document).on('click', '#analyze-webp-potential', function(e) {
                e.preventDefault();
                console.log('📊 Analyze WebP Potential button clicked!');
                WebPAdmin.analyzeWebPPotential();
            });

            $(document).on('click', '#test-webp-support', function(e) {
                e.preventDefault();
                console.log('🧪 Test WebP Support button clicked!');
                WebPAdmin.testWebPSupport();
            });

            // Test modal button
            $(document).on('click', '#test-webp-modal', function(e) {
                e.preventDefault();
                WebPAdmin.showModal();
            });

            // Debug image detection button
            $(document).on('click', '#debug-image-detection', function(e) {
                e.preventDefault();
                WebPAdmin.debugImageDetection();
            });

            // Simple AJAX test button
            $(document).on('click', '#test-simple-ajax', function(e) {
                e.preventDefault();
                WebPAdmin.testSimpleAjax();
            });

            // Test image detection button
            $(document).on('click', '#test-image-detection', function(e) {
                e.preventDefault();
                WebPAdmin.testImageDetection();
            });

            // Verify WebP files button
            $(document).on('click', '#verify-webp-files', function(e) {
                e.preventDefault();
                WebPAdmin.verifyWebPFiles();
            });

            // Reset WebP conversions button
            $(document).on('click', '#reset-webp-conversions', function(e) {
                e.preventDefault();
                if (confirm('Are you sure you want to reset all WebP conversion data? This will allow images to be re-converted with new settings.')) {
                    WebPAdmin.resetWebPConversions();
                }
            });

            // Reset specific images button
            $(document).on('click', '#reset-specific-images', function(e) {
                e.preventDefault();
                WebPAdmin.resetSpecificImages();
            });

            // Test WebP creation button
            $(document).on('click', '#test-webp-creation', function(e) {
                e.preventDefault();
                WebPAdmin.testWebPCreation();
            });

            // Check Image Status button
            $(document).on('click', '#check-image-status', function(e) {
                e.preventDefault();
                WebPAdmin.checkImageStatus();
            });

            // Enhanced WebP Test button
            $(document).on('click', '#test-enhanced-webp', function(e) {
                e.preventDefault();
                WebPAdmin.testEnhancedWebP();
            });

            // Test server support button
            $('#test-webp-support').on('click', this.testServerSupport.bind(this));

            // Modal close buttons
            $('#close-conversion-modal, #cancel-conversion').on('click', this.closeModal.bind(this));

            // Quality slider - update display only, auto-save is handled globally
            $('#quality').on('input', function() {
                $('#quality-value').text($(this).val());
            });

            // Lossless toggle
            $('#lossless').on('change', function() {
                if ($(this).is(':checked')) {
                    $('#quality-setting').slideUp();
                } else {
                    $('#quality-setting').slideDown();
                }
            });

            // WebP checkbox change handlers - use event delegation to avoid conflicts with auto-save
            $(document).on('change', '.webp-checkbox', this.updateWebPSummary.bind(this));

            // Auto-refresh stats every 30 seconds
            setInterval(this.updateStats.bind(this), 30000);

            // REAL-TIME STATS: Check for upload-triggered stats refresh every 5 seconds
            setInterval(this.checkForStatsRefresh.bind(this), 5000);

            // Initialize summary
            this.updateWebPSummary();
        },

        // Start bulk conversion process (INSTANT START - no delays)
        startBulkConversion: function() {
            if (this.isConverting) {
                alert('Conversion already in progress!');
                return;
            }

            // Show modal instantly
            this.showModal();
            this.isConverting = true;

            // SIMPLE FIX: Get all processable images upfront before starting
            const batchSize = this.getBatchSizeFromSettings();

            console.log('🔧 SIMPLE FIX: Getting all processable images before starting...');

            $.ajax({
                url: redcoWebP.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_webp_get_processable_images',
                    nonce: redcoWebP.nonces.bulk_convert
                },
                success: (response) => {
                    if (response.success && response.data) {
                        const imageIds = response.data.image_ids;
                        const totalImages = response.data.total_images;

                        console.log('🔧 SIMPLE FIX: Found', totalImages, 'processable images');

                        // Reset conversion data with pre-validated image list
                        this.conversionData = {
                            totalImages: totalImages,
                            processedImages: 0,
                            errorCount: 0,
                            totalSavings: 0,
                            currentBatch: 0,
                            batchSize: batchSize,
                            currentOffset: 0,
                            imageIds: imageIds // SIMPLE FIX: Store all image IDs upfront
                        };

                        // CRITICAL FIX: Add starting log entry
                        const logContainer = $('.log-entries');
                        if (logContainer.length > 0) {
                            const startEntry = $('<div class="log-entry log-info">')
                                .html(`<span class="dashicons dashicons-update"></span> ` +
                                      `<strong>Starting bulk conversion of ${totalImages} images...</strong>`);
                            logContainer.append(startEntry);
                            this.updateLogCount();
                        }

                        // Start processing with pre-validated list
                        this.processImageBatch(0);
                    } else {
                        console.error('❌ Failed to get processable images:', response);
                        this.handleError('Failed to get processable images: ' + (response.data || 'Unknown error'));
                    }
                },
                error: (xhr, status, error) => {
                    console.error('❌ AJAX error getting processable images:', error);
                    this.handleError('Failed to get processable images: ' + error);
                }
            });

            // Update modal title
            $('#webp-progress-modal-title').text('WebP Conversion');
            $('.current-file').text('Starting bulk conversion...');
        },

        // BATCH CALCULATION FIX: Get convertible images count before starting conversion
        getConvertibleImagesCount: function() {
            return new Promise((resolve, reject) => {
                $.ajax({
                    url: redcoWebP.ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'redco_webp_get_convertible_count',
                        nonce: redcoWebP.nonces.bulk_convert
                    },
                    success: function(response) {
                        if (response.success && response.data) {
                            const count = response.data.convertible_count || 0;
                            console.log('🔧 BATCH CALCULATION: Got convertible images count:', count);
                            resolve(count);
                        } else {
                            console.error('❌ Failed to get convertible images count:', response);
                            reject(new Error('Failed to get convertible images count'));
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('❌ AJAX error getting convertible images count:', error);
                        reject(new Error('AJAX error: ' + error));
                    }
                });
            });
        },

        // FIXED: Get batch size from settings
        getBatchSizeFromSettings: function() {
            // Try to get from settings form first
            const settingsBatchSize = $('#batch_size').val();
            if (settingsBatchSize) {
                const size = parseInt(settingsBatchSize);
                if (size >= 5 && size <= 50) {
                    console.log('🔧 Using batch size from settings:', size);
                    return size;
                }
            }

            // PERFORMANCE FIX: Increase default batch size for faster processing
            console.log('🔧 Using default batch size: 20');
            return 20;
        },

        // SIMPLE FIX: Process images from pre-validated list (no runtime checks)
        processImageBatch: function(offset) {
            const self = this;

            // Check if we have more images to process
            if (offset >= this.conversionData.imageIds.length) {
                console.log('🏁 SIMPLE FIX: All images processed!');
                this.completeConversion();
                return;
            }

            // Get batch of image IDs to process
            const batchSize = this.conversionData.batchSize;
            const imageBatch = this.conversionData.imageIds.slice(offset, offset + batchSize);
            const batchNumber = Math.floor(offset / batchSize) + 1;
            const totalBatches = Math.ceil(this.conversionData.imageIds.length / batchSize);

            console.log(`🔧 SIMPLE FIX: Processing batch ${batchNumber}/${totalBatches} (${imageBatch.length} images)`);

            // Update progress display
            $('.current-file').text(`Processing batch ${batchNumber}/${totalBatches}...`);

            // CRITICAL FIX: Add batch start log entry
            const logContainer = $('.log-entries');
            if (logContainer.length > 0) {
                const batchEntry = $('<div class="log-entry log-info">')
                    .html(`<span class="dashicons dashicons-admin-tools"></span> ` +
                          `<strong>Processing batch ${batchNumber}/${totalBatches}</strong><br>` +
                          `<small>Converting ${imageBatch.length} images...</small>`);
                logContainer.append(batchEntry);
                logContainer.scrollTop(logContainer[0].scrollHeight);
                this.updateLogCount();
            }

            $.ajax({
                url: redcoWebP.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_webp_bulk_convert',
                    batch_size: batchSize,
                    offset: offset,
                    image_ids: imageBatch, // SIMPLE FIX: Send specific image IDs to process
                    nonce: redcoWebP.nonces.bulk_convert
                },
                success: function(response) {
                    if (response.success && response.data) {
                        self.handleBatchSuccess(response.data, offset);

                        // Continue with next batch
                        const nextOffset = offset + batchSize;
                        if (nextOffset < self.conversionData.imageIds.length && self.isConverting) {
                            console.log('🔄 SIMPLE FIX: Continuing to next batch...');
                            self.processImageBatch(nextOffset);
                        } else {
                            console.log('🏁 SIMPLE FIX: All batches completed!');
                            self.completeConversion();
                        }
                    } else {
                        console.error('❌ Batch processing failed:', response);
                        self.handleError('Batch processing failed: ' + (response.data || 'Unknown error'));
                    }
                },
                error: function(xhr, status, error) {
                    console.error('❌ AJAX error in batch processing:', error);
                    self.handleError('Network error during conversion: ' + error);
                }
            });
        },

        // SIMPLE FIX: Complete the conversion process
        completeConversion: function() {
            console.log('🎉 SIMPLE FIX: Conversion completed successfully!');

            // Update progress to 100%
            $('.progress-fill').css('width', '100%');
            $('.progress-current').text(this.conversionData.totalImages);
            $('.progress-total').text(this.conversionData.totalImages);
            $('.current-file').text('Conversion completed successfully!');

            // Show completion message
            const processed = this.conversionData.processedImages;
            const total = this.conversionData.totalImages;
            const errors = this.conversionData.errorCount;

            $('.conversion-complete').show();
            $('.conversion-complete .conversion-summary').html(`
                ✅ Conversion Complete!<br>
                📸 Processed: ${processed} images<br>
                💾 Total Savings: ${this.formatFileSize(this.conversionData.totalSavings)}<br>
                ${errors > 0 ? `⚠️ Errors: ${errors}` : ''}
            `);

            // CRITICAL FIX: Switch from Cancel to Close button after completion
            $('#cancel-conversion').hide();
            $('#close-conversion-modal').show();

            // Update modal title to show completion
            $('#webp-progress-modal-title').text('WebP Conversion Complete');

            // CRITICAL FIX: Add completion log entry (was missing!)
            const logContainer = $('.log-entries');
            if (logContainer.length > 0) {
                const completionEntry = $('<div class="log-entry log-complete">')
                    .html(`<span class="dashicons dashicons-yes-alt"></span> ` +
                          `<strong>Bulk conversion completed successfully!</strong>`);
                logContainer.append(completionEntry);
                logContainer.scrollTop(logContainer[0].scrollHeight);

                // Update log count
                this.updateLogCount();
            }

            // Stop the conversion flag
            this.isConverting = false;

            // Refresh stats after completion
            this.refreshStats();

            // CRITICAL FIX: Update button state after conversion completion
            setTimeout(() => {
                this.updateConvertButtonState();
            }, 1000); // Small delay to allow stats to update first
        },

        // SIMPLE FIX: Handle batch success for simple approach
        handleBatchSuccess: function(data, offset) {
            // Update conversion data with batch results
            this.conversionData.processedImages += data.processed || 0;
            this.conversionData.errorCount += (data.errors ? data.errors.length : 0);

            // Calculate total savings from conversions
            if (data.conversions && data.conversions.length > 0) {
                data.conversions.forEach((conversion) => {
                    this.conversionData.totalSavings += (conversion.savings || 0);
                });
            }

            // CRITICAL FIX: Add conversion log entries (was missing!)
            this.logEnhancedConversions(data.conversions || [], data.errors || []);

            // Log debug info if available
            if (data.debug_info && data.debug_info.length > 0) {
                this.logDebugInfo(data.debug_info);
            }

            // Update progress display
            const processed = this.conversionData.processedImages;
            const total = this.conversionData.totalImages;
            const percentage = total > 0 ? Math.round((processed / total) * 100) : 0;

            $('.progress-fill').css('width', percentage + '%');
            $('.progress-current').text(processed);
            $('.progress-total').text(total);
            $('.processed-count').text(processed);
            $('.error-count').text(this.conversionData.errorCount);
            $('.total-savings').text(this.formatFileSize(this.conversionData.totalSavings));

            console.log(`🔧 SIMPLE FIX: Progress updated - ${processed}/${total} images (${percentage}%)`);
        },

        // Process Enhanced Batch (now the main conversion method)
        processEnhancedBatch: function(offset) {
            const self = this;

            // CRITICAL FIX: Update current offset tracking
            this.conversionData.currentOffset = offset;

            // BATCH CALCULATION FIX: Calculate batch numbers correctly
            const batchNumber = Math.floor(offset / self.conversionData.batchSize) + 1;
            const totalImages = self.conversionData.totalImages || 0;
            const totalBatches = totalImages > 0 ? Math.ceil(totalImages / self.conversionData.batchSize) : 1;

            // REAL-TIME FEEDBACK: Show detailed progress messages with correct batch info
            const progressMessages = [
                `🔍 Scanning batch ${batchNumber}/${totalBatches} for convertible images...`,
                `📊 Analyzing image formats and sizes...`,
                `🚀 Starting WebP conversion process...`,
                `⚡ Processing ${self.conversionData.batchSize} images in parallel...`,
                `🔧 Optimizing image quality and compression...`,
                `💾 Saving converted WebP files...`,
                `📈 Calculating file size savings...`,
                `🔄 Preparing next batch...`
            ];

            console.log(`🔧 BATCH CALCULATION: Starting batch ${batchNumber}/${totalBatches} (offset: ${offset}, batch size: ${self.conversionData.batchSize})`);
            console.log(`🔧 BATCH CALCULATION: Total images to convert: ${totalImages}`);

            let messageIndex = 0;
            $('.current-file').text(progressMessages[messageIndex]);

            // Update progress message every 800ms to show activity
            const progressInterval = setInterval(() => {
                messageIndex = (messageIndex + 1) % progressMessages.length;
                $('.current-file').text(progressMessages[messageIndex]);
            }, 800);

            $.ajax({
                url: redcoWebP.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_webp_bulk_convert',
                    batch_size: self.conversionData.batchSize,
                    offset: offset,
                    nonce: redcoWebP.nonces.bulk_convert
                },
                success: function(response) {
                    // REAL-TIME FEEDBACK: Clear progress interval and show result
                    clearInterval(progressInterval);

                    if (response.success) {
                        $('.current-file').text(`✅ Batch ${batchNumber} completed successfully!`);
                        self.handleEnhancedBatchSuccess(response);
                    } else {
                        // Enhanced error handling with specific error codes
                        let errorMsg = 'Conversion failed';
                        let debugInfo = '';

                        if (response.data) {
                            if (response.data.message) {
                                errorMsg = response.data.message;
                            }

                            if (response.data.error_code) {
                                switch (response.data.error_code) {
                                    case 'NONCE_FAILED':
                                        errorMsg = 'Security verification failed. Please refresh the page and try again.';
                                        break;
                                    case 'INSUFFICIENT_PERMISSIONS':
                                        errorMsg = 'You do not have sufficient permissions to perform this action.';
                                        break;
                                    case 'MODULE_DISABLED':
                                        errorMsg = 'WebP conversion module is not enabled. Please enable it in the Modules page.';
                                        break;
                                    case 'WEBP_NOT_SUPPORTED':
                                        errorMsg = 'Your server does not support WebP conversion. Please contact your hosting provider.';
                                        break;
                                    case 'INVALID_BATCH_SIZE':
                                        errorMsg = 'Invalid batch size. Please refresh the page and try again.';
                                        break;
                                }
                            }

                            if (response.data.debug_info) {
                                debugInfo = response.data.debug_info;
                                console.error('WebP Conversion Debug Info:', debugInfo);
                            }
                        }

                        self.handleError(errorMsg);
                    }
                },
                error: function(xhr, status, error) {
                    let errorMsg = 'Network error during conversion';
                    let debugInfo = '';

                    // Enhanced network error handling
                    if (xhr.status === 400) {
                        errorMsg = 'Bad request. Please check your settings and try again.';
                        debugInfo = 'HTTP 400: The request was malformed or invalid.';
                    } else if (xhr.status === 403) {
                        errorMsg = 'Access denied. Please check your permissions.';
                        debugInfo = 'HTTP 403: Insufficient permissions for this action.';
                    } else if (xhr.status === 404) {
                        errorMsg = 'Service not found. Please refresh the page and try again.';
                        debugInfo = 'HTTP 404: AJAX endpoint not found.';
                    } else if (xhr.status === 500) {
                        errorMsg = 'Server error. Please try again or contact support.';
                        debugInfo = 'HTTP 500: Internal server error occurred.';
                    } else if (xhr.status === 0) {
                        errorMsg = 'Connection failed. Please check your internet connection.';
                        debugInfo = 'Network connection failed or request was aborted.';
                    } else {
                        errorMsg = `Network error (${xhr.status}). Please try again.`;
                        debugInfo = `HTTP ${xhr.status}: ${xhr.statusText}`;
                    }

                    console.error('WebP Conversion Network Error:', {
                        status: xhr.status,
                        statusText: xhr.statusText,
                        responseText: xhr.responseText,
                        error: error,
                        debugInfo: debugInfo
                    });

                    // REAL-TIME FEEDBACK: Clear progress interval on error
                    clearInterval(progressInterval);
                    $('.current-file').text('❌ ' + errorMsg);

                    self.handleError(errorMsg);
                }
            });
        },

        // Handle successful enhanced batch conversion
        handleEnhancedBatchSuccess: function(response) {
            const data = response.data;

            // Update conversion data with real results
            this.conversionData.processedImages = data.total_processed;
            this.conversionData.errorCount += (data.errors ? data.errors.length : 0);

            // Calculate total savings from real conversions
            const batchSavings = (data.conversions || []).reduce((total, conversion) => {
                return total + (conversion.savings || 0);
            }, 0);
            this.conversionData.totalSavings += batchSavings;

            // Update progress with real data
            this.updateEnhancedProgress(data);

            // Log real conversions and errors
            this.logEnhancedConversions(data.conversions || [], data.errors || []);

            // Log debug info if available
            if (data.debug_info && data.debug_info.length > 0) {
                this.logDebugInfo(data.debug_info);
            }

            // ENHANCED DEBUG: Log decision logic
            console.log('🔍 BATCH DECISION: has_more =', data.has_more, ', isConverting =', this.isConverting);
            console.log('🔍 BATCH DECISION: total_processed =', data.total_processed, ', processed =', data.processed);

            // Continue with next batch if there are more images
            if (data.has_more && this.isConverting) {
                console.log('🔄 CONTINUING: More images to process, starting next batch immediately...');

                // BATCH CALCULATION FIX: Show accurate batch transition message
                const totalImages = this.conversionData.totalImages || data.total_images || data.total_processed;
                const batchSize = this.conversionData.batchSize || 10;
                const nextBatchNumber = Math.floor(data.total_processed / batchSize) + 1;
                const totalBatches = Math.ceil(totalImages / batchSize);

                $('.current-file').text(`🔄 Preparing batch ${nextBatchNumber}/${totalBatches}... (${data.total_processed}/${totalImages} images processed)`);

                // CRITICAL FIX: Use the server's calculated next offset
                const nextOffset = data.next_offset || (this.conversionData.currentOffset + batchSize);
                this.conversionData.currentOffset = nextOffset;

                console.log(`🔧 OFFSET: Next offset: ${nextOffset} (server calculated)`);

                // PERFORMANCE FIX: Remove 1.5 second delay for faster processing
                this.processEnhancedBatch(nextOffset);
            } else {
                console.log('🏁 COMPLETING: No more images or conversion stopped');
                console.log('🔍 Reason: has_more =', data.has_more, ', isConverting =', this.isConverting);

                // CRITICAL DEBUG: Log completion details
                const totalImages = this.conversionData.totalImages || data.total_images;
                const processed = data.total_processed;
                const remaining = totalImages - processed;

                console.log(`🔍 COMPLETION DETAILS: Processed ${processed}/${totalImages} images, ${remaining} remaining`);

                if (remaining > 0 && !data.has_more) {
                    console.warn(`⚠️ INCOMPLETE CONVERSION: ${remaining} images left unconverted but has_more=false`);
                    console.warn('🔍 This suggests file validation or database query issues');
                }

                this.completeEnhancedConversion();
            }
        },

        // Update enhanced progress display
        updateEnhancedProgress: function(data) {
            const processed = data.total_processed;

            // CRITICAL FIX: Always use the initially set totalImages, never update it from server responses
            // The server's get_convertible_images_count() can return different values as images are converted
            const total = this.conversionData.totalImages || data.total_images || processed;

            // CRITICAL FIX: Only set totalImages once at the beginning, never update it during conversion
            if (data.total_images && !this.conversionData.totalImages) {
                this.conversionData.totalImages = data.total_images;
                console.log('🔧 BATCH CALCULATION: Set total images to', data.total_images);
            }

            // CRITICAL DEBUG: Log if server is trying to change the total
            if (data.total_images && this.conversionData.totalImages && data.total_images !== this.conversionData.totalImages) {
                console.warn('⚠️ BATCH CALCULATION: Server returned different total_images!', {
                    stored: this.conversionData.totalImages,
                    server: data.total_images,
                    processed: processed
                });
                console.warn('⚠️ BATCH CALCULATION: Keeping original total to maintain consistency');
            }

            // BATCH CALCULATION FIX: Calculate current batch number correctly
            const batchSize = this.conversionData.batchSize || 10;
            const currentBatch = Math.floor(processed / batchSize) + 1;
            const totalBatches = Math.ceil(total / batchSize);

            // Update progress bar with real data
            const percentage = total > 0 ? Math.round((processed / total) * 100) : 0;
            $('.progress-fill').css('width', percentage + '%');

            // Update progress text with real numbers
            $('.progress-current').text(processed);
            $('.progress-total').text(total);

            // Update stats with real data
            $('.processed-count').text(processed);
            $('.error-count').text(this.conversionData.errorCount);
            $('.total-savings').text(this.formatFileSize(this.conversionData.totalSavings));

            // BATCH CALCULATION FIX: Log accurate batch information
            console.log(`🔧 BATCH PROGRESS: Batch ${currentBatch}/${totalBatches} - ${processed}/${total} images (${percentage}%)`);

            // BATCH CALCULATION FIX: Update batch completion message with correct numbers
            if (data.conversions && data.conversions.length > 0) {
                const batchProcessed = data.conversions.length;
                console.log(`✅ Batch ${currentBatch} completed successfully! Processed ${batchProcessed} images in this batch.`);
            }

            // REAL-TIME FEEDBACK: Update current operation and progress messages
            if (data.current_operation) {
                $('.current-file').text(data.current_operation);
            } else if (data.current_file) {
                $('.current-file').text(`Processing: ${data.current_file}`);
            }

            // Show latest progress message if available
            if (data.progress_messages && data.progress_messages.length > 0) {
                const latestMessage = data.progress_messages[data.progress_messages.length - 1];
                $('.current-file').text(latestMessage);
            }
        },

        // Log enhanced conversion results with detailed information
        logEnhancedConversions: function(conversions, errors) {
            const logContainer = $('.log-entries');

            // Safety check for log container
            if (logContainer.length === 0) {
                return;
            }

            // Log successful conversions with detailed info
            conversions.forEach((conversion) => {
                const logEntry = $('<div class="log-entry log-success">')
                    .html(`<span class="dashicons dashicons-yes"></span> ` +
                          `<strong>${conversion.title}</strong><br>` +
                          `<small>Original: ${this.formatFileSize(conversion.original_size)} → ` +
                          `WebP: ${this.formatFileSize(conversion.webp_size)} ` +
                          `(${conversion.savings_percentage}% saved)</small>`);
                logContainer.append(logEntry);
            });

            // Log errors with detailed info
            errors.forEach((error) => {
                const logEntry = $('<div class="log-entry log-error">')
                    .html(`<span class="dashicons dashicons-no"></span> ` +
                          `<strong>${error.title}</strong><br>` +
                          `<small>Error: ${error.error}</small>`);
                logContainer.append(logEntry);
            });

            // Scroll to bottom (with safety check)
            if (logContainer[0]) {
                logContainer.scrollTop(logContainer[0].scrollHeight);
            }

            // Update log count
            this.updateLogCount();
        },

        // Log debug information
        logDebugInfo: function(debugInfo) {
            const logContainer = $('.log-entries');
            if (logContainer.length === 0) return;

            debugInfo.forEach((info) => {
                const logEntry = $('<div class="log-entry log-debug">')
                    .html(`<span class="dashicons dashicons-info"></span> ` +
                          `<small>${info}</small>`);
                logContainer.append(logEntry);
            });

            // Scroll to bottom
            if (logContainer[0]) {
                logContainer.scrollTop(logContainer[0].scrollHeight);
            }

            // Update log count
            this.updateLogCount();
        },

        // Complete enhanced conversion process
        completeEnhancedConversion: function() {
            this.isConverting = false;

            // CRITICAL FIX: Switch from Cancel to Close button after completion
            $('#cancel-conversion').hide();
            $('#close-conversion-modal').show();

            // Update modal title to show completion
            $('#webp-progress-modal-title').text('WebP Conversion Complete');

            // CRITICAL: Update all stats and UI elements with fresh data
            console.log('🔄 Bulk conversion complete - refreshing all statistics...');
            this.updateStats();

            // Show detailed completion message
            const processed = this.conversionData.processedImages;
            const errors = this.conversionData.errorCount;
            const savings = this.conversionData.totalSavings;

            let message = `<strong>✅ Conversion Complete!</strong><br>`;
            message += `📊 Processed: ${processed} images<br>`;

            if (savings > 0) {
                message += `💾 Total Savings: ${this.formatFileSize(savings)}<br>`;
            }

            if (errors > 0) {
                message += `⚠️ Errors: ${errors} images failed<br>`;
            }

            message += `<br><em>Check the log above for detailed results.</em>`;

            $('.current-file').html(message);

            // Add completion log entry
            const logContainer = $('.log-entries');
            if (logContainer.length > 0) {
                const completionEntry = $('<div class="log-entry log-complete">')
                    .html(`<span class="dashicons dashicons-yes-alt"></span> ` +
                          `<strong>Bulk conversion completed successfully!</strong>`);
                logContainer.append(completionEntry);
                logContainer.scrollTop(logContainer[0].scrollHeight);

                // Update log count
                this.updateLogCount();
            }

            // Trigger completion event for enhanced features
            $(document).trigger('webp-conversion-completed', {
                summary: {
                    total_processed: processed,
                    successful: processed - errors,
                    failed: errors,
                    total_savings: this.formatFileSize(savings),
                    average_savings: this.conversionData.averageSavings || '0%'
                },
                timestamp: new Date().toISOString()
            });

            // Show toast notification
            if (typeof showToast === 'function') {
                showToast(`Successfully converted ${processed} images!`, 'success', 8000);
            }
        },

        // Test server support
        testServerSupport: function() {
            var button = $('#test-webp-support');
            var originalText = button.text();

            button.prop('disabled', true).text('Testing...');

            $.ajax({
                url: redcoWebP.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_webp_test_conversion',
                    nonce: redcoWebP.nonces.test
                },
                success: function(response) {
                    button.prop('disabled', false).text(originalText);

                    if (response.success) {
                        alert('✓ ' + response.message);
                    } else {
                        alert('✗ ' + response.message);
                    }

                    // Server capabilities tested
                },
                error: function() {
                    button.prop('disabled', false).text(originalText);
                    alert('Error testing server support');
                }
            });
        },

        // ENHANCED: Update all statistics and UI elements after bulk conversion
        updateStats: function() {
            console.log('🔄 Updating all WebP statistics and UI elements...');

            $.ajax({
                url: redcoWebP.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_webp_get_stats',
                    nonce: redcoWebP.nonces.stats
                },
                success: function(response) {
                    if (response.success !== false && response.data) {
                        const data = response.data;
                        console.log('📊 Stats data received:', data);

                        // STATS FIX: Update header metrics with correct selectors
                        $('.header-metric').eq(0).find('.header-metric-value').text((data.converted_images || 0).toLocaleString());
                        $('.header-metric').eq(1).find('.header-metric-value').text((data.conversion_percentage || 0) + '%');
                        $('.header-metric').eq(2).find('.header-metric-value').text(WebPAdmin.formatFileSize(data.total_savings || 0));

                        // SIMPLE STATS: Use actual database values
                        $('.stat-item.stat-total-files .stat-value').text((data.total_images || 0).toLocaleString());
                        $('.stat-item.stat-converted-files .stat-value').text((data.converted_images || 0).toLocaleString());
                        $('.stat-item.stat-total-savings .stat-value').text(WebPAdmin.formatFileSize(data.total_savings || 0));
                        $('.stat-item.stat-avg-savings .stat-value').text((data.savings_percentage || 0) + '%');

                        console.log('📊 Updated simple stats from database:', {
                            total_images: data.total_images,
                            converted_images: data.converted_images,
                            total_savings: data.total_savings,
                            savings_percentage: data.savings_percentage
                        });

                        // 3. Update conversion button state
                        WebPAdmin.updateConvertButtonState();

                        // 4. Refresh recent conversions list
                        WebPAdmin.loadRecentConversions();

                        // 5. Update any progress indicators
                        if (data.conversion_percentage !== undefined) {
                            $('.conversion-progress').css('width', data.conversion_percentage + '%');
                        }

                        // 6. Show success notification
                        if (typeof showToast === 'function') {
                            showToast('Statistics updated successfully!', 'success', 3000);
                        }

                        console.log('✅ All statistics updated successfully');
                    } else {
                        console.warn('⚠️ Invalid stats response:', response);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('❌ Stats update failed:', xhr.status, xhr.statusText);

                    // Show user-friendly error for critical failures
                    if (xhr.status >= 500) {
                        if (typeof showToast === 'function') {
                            showToast('Failed to refresh statistics. Please reload the page.', 'error', 5000);
                        }
                    }
                }
            });
        },

        // Update convert button state based on available images
        updateConvertButtonState: function() {
            console.log('🔄 Updating convert button state...');

            $.ajax({
                url: redcoWebP.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_webp_get_convertible_count',
                    nonce: redcoWebP.nonces.bulk_convert
                },
                success: function(response) {
                    if (response.success && response.data) {
                        const count = response.data.convertible_count || 0;
                        const $button = $('#bulk-convert-images');
                        const $buttonText = $('#convert-button-text');
                        const $spinner = $('#convert-button-spinner');

                        // Hide spinner
                        $spinner.hide();

                        if (count > 0) {
                            $button.prop('disabled', false);
                            $buttonText.text(`Convert ${count} Images`);
                            console.log(`✅ Button enabled for ${count} images`);
                        } else {
                            $button.prop('disabled', true);
                            $buttonText.text('No Images to Convert');
                            console.log('ℹ️ Button disabled - no convertible images');
                        }
                    }
                },
                error: function(xhr, status, error) {
                    console.error('❌ Failed to update button state:', error);
                    // Keep button in safe state
                    $('#bulk-convert-images').prop('disabled', true);
                    $('#convert-button-text').text('Error Checking Images');
                    $('#convert-button-spinner').hide();
                }
            });
        },

        // Load recent conversions list
        loadRecentConversions: function() {
            console.log('🔄 Loading recent conversions...');

            const $loadingDiv = $('#conversions-loading');
            const $listDiv = $('#recent-conversions-list');
            const $emptyDiv = $('#conversions-empty');

            // Show loading state
            $loadingDiv.show();
            $listDiv.hide();
            $emptyDiv.hide();

            $.ajax({
                url: redcoWebP.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_webp_get_recent_conversions',
                    limit: 10, // ENHANCED FIX: Show maximum 10 recent conversions
                    nonce: redcoWebP.nonces.stats
                },
                success: function(response) {
                    $loadingDiv.hide();

                    console.log('🔍 Recent conversions response:', response);

                    if (response.success && response.data && response.data.length > 0) {
                        // CLEAN OVERHAUL: Simple, clean conversion list
                        let html = '';
                        response.data.slice(0, 10).forEach(function(conversion) { // Limit to exactly 10 records
                            html += `
                                <div class="recent-conversion-item">
                                    <div class="conversion-main">
                                        <div class="conversion-title">${conversion.title}</div>
                                        <div class="conversion-meta">
                                            <span class="original-size">${conversion.formatted_original_size}</span>
                                            <span class="arrow">→</span>
                                            <span class="webp-size">${conversion.formatted_webp_size}</span>
                                            <span class="savings">${conversion.savings_percentage}% saved</span>
                                            <span class="date">${conversion.formatted_date}</span>
                                        </div>
                                    </div>
                                </div>
                            `;
                        });

                        $listDiv.html(html).show();
                        console.log(`✅ Loaded ${response.data.length} recent conversions`);
                    } else {
                        $emptyDiv.show();
                        console.log('ℹ️ No recent conversions found. Response:', response);

                        // RECENT CONVERSIONS DEBUG: Show detailed info about why no conversions found
                        if (response.success === false) {
                            console.warn('🔍 AJAX request failed:', response);
                        } else if (!response.data) {
                            console.warn('🔍 No data in response:', response);
                        } else if (response.data.length === 0) {
                            console.warn('🔍 Empty data array - no conversions exist in database');
                        }
                    }
                },
                error: function(xhr, status, error) {
                    console.error('❌ Failed to load recent conversions:', error);
                    $loadingDiv.hide();
                    $emptyDiv.show();
                }
            });
        },

        // REAL-TIME STATS: Check if statistics refresh is needed due to new uploads
        checkForStatsRefresh: function() {
            $.ajax({
                url: redcoWebP.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_webp_check_refresh_needed',
                    nonce: redcoWebP.nonces.stats
                },
                success: function(response) {
                    if (response.success && response.data && response.data.refresh_needed) {
                        console.log('📊 REAL-TIME STATS: Refresh needed due to new upload, triggering stats update...');
                        WebPAdmin.refreshStats();
                    }
                },
                error: function(xhr, status, error) {
                    // Silently fail - this is a background check
                }
            });
        },

        // REAL-TIME STATS: Force refresh statistics with visual feedback
        refreshStats: function() {
            console.log('📊 REAL-TIME STATS: Forcing statistics refresh...');

            // Add visual feedback
            $('.stat-value').addClass('updating');

            $.ajax({
                url: redcoWebP.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_webp_refresh_stats',
                    nonce: redcoWebP.nonces.stats
                },
                success: function(response) {
                    if (response.success && response.data && response.data.stats) {
                        const data = response.data.stats;
                        console.log('📊 REAL-TIME STATS: Fresh stats received:', data);

                        // Update header metrics with fresh data
                        $('.header-metric').eq(0).find('.header-metric-value').text((data.converted_images || 0).toLocaleString());
                        $('.header-metric').eq(1).find('.header-metric-value').text((data.conversion_percentage || 0) + '%');
                        $('.header-metric').eq(2).find('.header-metric-value').text(WebPAdmin.formatFileSize(data.total_savings || 0));

                        // SIMPLE STATS: Use actual database values
                        $('.stat-item.stat-total-files .stat-value').text((data.total_images || 0).toLocaleString());
                        $('.stat-item.stat-converted-files .stat-value').text((data.converted_images || 0).toLocaleString());
                        $('.stat-item.stat-total-savings .stat-value').text(WebPAdmin.formatFileSize(data.total_savings || 0));
                        $('.stat-item.stat-avg-savings .stat-value').text((data.savings_percentage || 0) + '%');

                        // REMOVED: Old stat selectors (now using last conversion stats)

                        // REAL-TIME DEBUG: Check simple stats update
                        console.log('📊 REAL-TIME SIMPLE STATS DEBUG:', {
                            total_images: data.total_images,
                            converted_images: data.converted_images,
                            total_savings: data.total_savings,
                            savings_percentage: data.savings_percentage
                        });

                        // Add visual feedback for updated values
                        $('.stat-value').removeClass('updating').addClass('updated');
                        setTimeout(function() {
                            $('.stat-value').removeClass('updated');
                        }, 2000);

                        console.log('📊 REAL-TIME STATS: Statistics updated successfully');
                    } else {
                        console.error('📊 REAL-TIME STATS: Failed to refresh stats:', response);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('📊 REAL-TIME STATS: Refresh error:', error);
                    $('.stat-value').removeClass('updating');
                }
            });
        },

        // Show conversion modal (INSTANT LOAD - no delays)
        showModal: function() {
            console.log('🔧 WebP showModal called');

            // CRITICAL DEBUG: Check if jQuery is available
            if (typeof $ === 'undefined') {
                console.error('❌ jQuery is not available!');
                alert('Error: jQuery is not loaded. Please refresh the page.');
                return;
            }

            // Ensure modal exists first
            this.initProgressModal();

            const $modal = $('#webp-progress-modal');
            console.log('🔧 Modal element found:', $modal.length);
            console.log('🔧 Modal HTML:', $modal.length > 0 ? $modal[0].outerHTML.substring(0, 200) + '...' : 'Not found');

            if ($modal.length === 0) {
                console.error('❌ Modal element still not found after creation');
                console.error('❌ Available elements with "modal" in ID:', $('[id*="modal"]').length);
                alert('Modal creation failed! Check console for details.');
                return;
            }

            console.log('🔧 Showing modal instantly...');
            $('#webp-progress-modal-title').text('Converting Images to WebP');
            $modal.show(); // INSTANT SHOW - no delays
            console.log('🔧 Modal display style after show():', $modal.css('display'));

            // CRITICAL FIX: Ensure correct button states at start
            $('#cancel-conversion').show(); // Show Cancel during conversion
            $('#close-conversion-modal').hide(); // Hide Close until completion

            // Reset modal content
            $('.progress-fill').css('width', '0%');
            $('.progress-current').text('0');
            $('.progress-total').text('0');
            $('.processed-count').text('0');
            $('.error-count').text('0');
            $('.total-savings').text('0 KB');
            $('.log-entries').empty();
            $('.current-file').text('Preparing conversion...');

            // Reset log section to collapsed state
            $('#conversion-log').removeClass('expanded').addClass('collapsed');
            $('#log-toggle .dashicons').removeClass('dashicons-arrow-down-alt2').addClass('dashicons-arrow-right-alt2');
            $('.log-count').text('(0 entries)');

            // Show/hide buttons
            $('#cancel-conversion').show();

            console.log('✅ Modal should now be visible');
        },

        // Debug image detection
        debugImageDetection: function() {
            console.log('🔧 Testing image detection...');

            $.ajax({
                url: redcoWebP.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_webp_debug_images',
                    nonce: redcoWebP.nonce || redcoWebP.nonces.stats
                },
                success: function(response) {
                    if (response.success) {
                        alert('Debug Results:\n' +
                              'Total Images: ' + response.data.total_images + '\n' +
                              'Image IDs: ' + response.data.image_ids.join(', ') + '\n' +
                              'MIME Types: ' + response.data.mime_types.join(', '));
                    } else {
                        alert('Debug failed: ' + response.data);
                    }
                },
                error: function(xhr, status, error) {

                    alert('Debug request failed: ' + xhr.status + ' ' + xhr.statusText);
                }
            });
        },

        // Test simple AJAX (no nonce required)
        testSimpleAjax: function() {
            console.log('🔧 Testing simple AJAX...');

            $.ajax({
                url: redcoWebP.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_webp_test_simple'
                },
                success: function(response) {
                    console.log('✅ Simple AJAX response:', response);
                    alert('Simple AJAX Test Result:\n' + response.data.message);
                },
                error: function(xhr, status, error) {
                    console.error('❌ Simple AJAX failed:', {
                        status: xhr.status,
                        statusText: xhr.statusText,
                        responseText: xhr.responseText,
                        error: error
                    });
                    alert('Simple AJAX failed: ' + xhr.status + ' ' + xhr.statusText);
                }
            });
        },

        // Verify WebP files exist on disk
        verifyWebPFiles: function() {
            console.log('🔧 Verifying WebP files on disk...');

            $.ajax({
                url: redcoWebP.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_webp_verify_files',
                    nonce: redcoWebP.nonces.stats
                },
                success: function(response) {
                    console.log('✅ WebP file verification response:', response);
                    if (response.success) {
                        const data = response.data;
                        let message = 'WebP File Verification Results:\n\n';
                        message += 'Database Records: ' + data.database_records + '\n';
                        message += 'Files Found on Disk: ' + data.files_found + '\n';
                        message += 'Missing Files: ' + data.missing_files + '\n';
                        message += 'Empty Files: ' + data.empty_files + '\n\n';

                        if (data.missing_file_list && data.missing_file_list.length > 0) {
                            message += 'Missing Files:\n';
                            data.missing_file_list.forEach(function(file) {
                                message += '- ' + file + '\n';
                            });
                        }

                        alert(message);
                    } else {
                        alert('Verification failed: ' + response.data);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('❌ WebP verification failed:', xhr.status, xhr.statusText);
                    alert('Verification request failed: ' + xhr.status + ' ' + xhr.statusText);
                }
            });
        },

        // Reset WebP conversion data
        resetWebPConversions: function() {
            console.log('🔧 Resetting WebP conversion data...');

            $.ajax({
                url: redcoWebP.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_webp_reset_conversions',
                    nonce: redcoWebP.nonces.stats
                },
                success: function(response) {
                    console.log('✅ WebP reset response:', response);
                    if (response.success) {
                        const data = response.data;
                        let message = 'WebP Conversion Reset Results:\n\n';
                        message += 'Database Records Cleared: ' + data.records_cleared + '\n';
                        message += 'WebP Files Deleted: ' + data.files_deleted + '\n';
                        message += 'Conversion Log Cleared: ' + (data.log_cleared ? 'Yes' : 'No') + '\n\n';
                        message += 'You can now re-convert images with new settings.';

                        alert(message);

                        // Refresh the page to update statistics
                        location.reload();
                    } else {
                        alert('Reset failed: ' + response.data);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('❌ WebP reset failed:', xhr.status, xhr.statusText);
                    alert('Reset request failed: ' + xhr.status + ' ' + xhr.statusText);
                }
            });
        },

        // Test WebP creation with actual file
        testWebPCreation: function() {
            console.log('🔧 Testing WebP creation with actual file...');

            $.ajax({
                url: redcoWebP.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_webp_test_creation',
                    nonce: redcoWebP.nonces.stats
                },
                success: function(response) {
                    console.log('✅ WebP creation test response:', response);
                    if (response.success) {
                        const data = response.data;
                        let message = 'WebP Creation Test Results:\n\n';
                        message += 'Test Image Found: ' + (data.test_image_found ? 'Yes' : 'No') + '\n';
                        message += 'Test Image Path: ' + (data.test_image_path || 'N/A') + '\n';
                        message += 'WebP Support: ' + (data.webp_support ? 'Yes' : 'No') + '\n';
                        message += 'Directory Writable: ' + (data.directory_writable ? 'Yes' : 'No') + '\n';
                        message += 'Conversion Attempted: ' + (data.conversion_attempted ? 'Yes' : 'No') + '\n';
                        message += 'imagewebp() Success: ' + (data.imagewebp_success ? 'Yes' : 'No') + '\n';
                        message += 'File Created: ' + (data.file_created ? 'Yes' : 'No') + '\n';
                        message += 'File Size: ' + (data.file_size || 'N/A') + '\n';
                        message += 'Valid WebP: ' + (data.valid_webp ? 'Yes' : 'No') + '\n\n';

                        if (data.error_details) {
                            message += 'Error Details:\n' + data.error_details + '\n\n';
                        }

                        if (data.php_errors && data.php_errors.length > 0) {
                            message += 'PHP Errors:\n';
                            data.php_errors.forEach(function(error) {
                                message += '- ' + error + '\n';
                            });
                        }

                        alert(message);
                    } else {
                        alert('WebP creation test failed: ' + response.data);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('❌ WebP creation test failed:', xhr.status, xhr.statusText);
                    alert('WebP creation test request failed: ' + xhr.status + ' ' + xhr.statusText);
                }
            });
        },

        // Reset specific images based on filenames
        resetSpecificImages: function() {
            console.log('🔧 Resetting specific images...');

            // The 3 problematic images from the log
            const imageFilenames = [
                '133913847558663271-scaled.jpg',
                '133921112396416462-scaled.jpg',
                'login-logo.jpg'
            ];

            const confirmMessage = 'Reset conversion data for these 3 images?\n\n' +
                                 imageFilenames.join('\n') +
                                 '\n\nThis will allow them to be re-converted with proper settings.';

            if (!confirm(confirmMessage)) {
                return;
            }

            $.ajax({
                url: redcoWebP.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_webp_reset_specific_images',
                    nonce: redcoWebP.nonces.stats,
                    filenames: imageFilenames
                },
                success: function(response) {
                    console.log('✅ Specific reset response:', response);
                    if (response.success) {
                        const data = response.data;
                        let message = 'Specific Images Reset Results:\n\n';
                        message += 'Images Found: ' + data.images_found + '\n';
                        message += 'Records Cleared: ' + data.records_cleared + '\n';
                        message += 'WebP Files Deleted: ' + data.files_deleted + '\n\n';

                        if (data.processed_images && data.processed_images.length > 0) {
                            message += 'Processed Images:\n';
                            data.processed_images.forEach(function(img) {
                                message += '- ' + img + '\n';
                            });
                        }

                        message += '\nThese images can now be re-converted.';

                        alert(message);

                        // Refresh the page to update statistics
                        location.reload();
                    } else {
                        alert('Specific reset failed: ' + response.data);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('❌ Specific reset failed:', xhr.status, xhr.statusText);
                    alert('Specific reset request failed: ' + xhr.status + ' ' + xhr.statusText);
                }
            });
        },

        // Test image detection query
        testImageDetection: function() {
            console.log('🔧 Testing image detection query...');

            $.ajax({
                url: redcoWebP.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_webp_test_image_detection'
                },
                success: function(response) {
                    console.log('✅ Image detection test response:', response);
                    if (response.success) {
                        const data = response.data;
                        let message = 'Image Detection Test Results:\n\n';
                        message += 'Total Images: ' + data.all_images_count + '\n';
                        message += 'Unconverted Images: ' + data.unconverted_images_count + '\n';
                        message += 'Existing Conversions: ' + data.existing_conversions_count + '\n\n';

                        if (data.all_images_count > 0) {
                            message += 'All Images:\n';
                            data.all_images.forEach(function(img, index) {
                                message += (index + 1) + '. ID: ' + img.ID + ' - ' + img.post_mime_type + ' - ' + (img.post_title || 'No title') + '\n';
                            });
                        }

                        if (data.unconverted_images_count > 0) {
                            message += '\nUnconverted Images:\n';
                            data.unconverted_images.forEach(function(img, index) {
                                message += (index + 1) + '. ID: ' + img.ID + ' - ' + img.post_mime_type + ' - ' + (img.post_title || 'No title') + '\n';
                            });
                        }

                        alert(message);
                    } else {
                        alert('Image detection test failed: ' + response.data);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('❌ Image detection test failed:', xhr.status, xhr.statusText);
                    alert('Image detection test failed: ' + xhr.status + ' ' + xhr.statusText);
                }
            });
        },

        // Check image status and conversion data
        checkImageStatus: function() {
            console.log('🔧 Checking image status...');

            $.ajax({
                url: redcoWebP.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_webp_debug_images',
                    nonce: redcoWebP.nonce || redcoWebP.nonces.stats
                },
                success: function(response) {
                    console.log('✅ Image status response:', response);
                    if (response.success) {
                        const data = response.data;
                        let message = '📊 IMAGE STATUS REPORT\n\n';
                        message += '📁 Total Attachments: ' + data.total_attachments + '\n';
                        message += '🖼️ Total Images: ' + data.total_images + '\n';
                        message += '🆔 Image IDs: ' + data.image_ids.join(', ') + '\n\n';

                        message += '📋 MIME Types Found:\n';
                        data.all_attachment_types.forEach(function(type) {
                            message += '  • ' + type + '\n';
                        });

                        message += '\n🔍 Image MIME Types:\n';
                        data.mime_types.forEach(function(type, index) {
                            message += '  • ID ' + data.image_ids[index] + ': ' + type + '\n';
                        });

                        alert(message);
                    } else {
                        alert('❌ Image status check failed: ' + response.data);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('❌ Image status check failed:', xhr.status, xhr.statusText);
                    alert('❌ Image status check failed: ' + xhr.status + ' ' + xhr.statusText);
                }
            });
        },

        // Close modal
        closeModal: function() {
            if (this.isConverting) {
                if (confirm('Are you sure you want to cancel the conversion?')) {
                    this.isConverting = false;
                    $('#webp-progress-modal').hide();
                }
            } else {
                $('#webp-progress-modal').hide();
            }
        },

        // Enhanced error handling with actionable feedback
        handleError: function(message) {
            this.isConverting = false;

            // Update modal title to indicate error
            $('#webp-progress-modal-title').text('WebP Conversion Error');

            // Create enhanced error message with actionable feedback
            let errorHtml = '<div style="color: #d63638; padding: 15px; background: #fef7f7; border: 1px solid #f5c6cb; border-radius: 4px;">';
            errorHtml += '<strong>❌ Conversion Failed</strong><br><br>';
            errorHtml += '<div style="margin-bottom: 10px;">' + message + '</div>';

            // Add actionable suggestions based on error type
            if (message.includes('Security verification failed') || message.includes('nonce')) {
                errorHtml += '<div style="margin-top: 10px; padding: 10px; background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 3px;">';
                errorHtml += '<strong>💡 Solution:</strong> Please refresh the page and try again.';
                errorHtml += '</div>';
            } else if (message.includes('permissions') || message.includes('Access denied')) {
                errorHtml += '<div style="margin-top: 10px; padding: 10px; background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 3px;">';
                errorHtml += '<strong>💡 Solution:</strong> Please contact your administrator to check user permissions.';
                errorHtml += '</div>';
            } else if (message.includes('module is not enabled') || message.includes('MODULE_DISABLED')) {
                errorHtml += '<div style="margin-top: 10px; padding: 10px; background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 3px;">';
                errorHtml += '<strong>💡 Solution:</strong> Go to the Modules page and enable the WebP conversion module.';
                errorHtml += '</div>';
            } else if (message.includes('server does not support') || message.includes('WebP')) {
                errorHtml += '<div style="margin-top: 10px; padding: 10px; background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 3px;">';
                errorHtml += '<strong>💡 Solution:</strong> Contact your hosting provider to enable GD library with WebP support.';
                errorHtml += '</div>';
            } else if (message.includes('Network error') || message.includes('Connection failed')) {
                errorHtml += '<div style="margin-top: 10px; padding: 10px; background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 3px;">';
                errorHtml += '<strong>💡 Solution:</strong> Check your internet connection and try again.';
                errorHtml += '</div>';
            } else {
                errorHtml += '<div style="margin-top: 10px; padding: 10px; background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 3px;">';
                errorHtml += '<strong>💡 Suggestions:</strong><br>';
                errorHtml += '• Refresh the page and try again<br>';
                errorHtml += '• Check if the WebP module is enabled<br>';
                errorHtml += '• Verify your server supports WebP conversion<br>';
                errorHtml += '• Contact support if the problem persists';
                errorHtml += '</div>';
            }

            errorHtml += '</div>';

            $('.current-file').html(errorHtml);

            // Add error to log
            const logContainer = $('.log-entries');
            if (logContainer.length > 0) {
                const errorEntry = $('<div class="log-entry log-error">')
                    .html('<span class="dashicons dashicons-warning"></span> <strong>Conversion stopped due to error:</strong><br>' +
                          '<small>' + message + '</small>');
                logContainer.append(errorEntry);
                logContainer.scrollTop(logContainer[0].scrollHeight);

                // Update log count
                this.updateLogCount();
            }

            // Update buttons
            $('#cancel-conversion').hide();
            $('#close-conversion-modal').show();

            // Log error for debugging
            console.error('WebP Conversion Error:', message);
        },

        // Update WebP settings summary
        updateWebPSummary: function() {
            var enabledCount = 0;
            var estimatedSavings = 0;

            // Count enabled optimizations
            $('.webp-checkbox:checked').each(function() {
                enabledCount++;

                // Calculate estimated savings based on option
                var optionName = $(this).attr('name');
                if (optionName && optionName.includes('auto_convert_uploads')) {
                    estimatedSavings += 30; // 30% average savings for new uploads
                } else if (optionName && optionName.includes('replace_in_content')) {
                    estimatedSavings += 25; // 25% average savings for content replacement
                } else if (optionName && optionName.includes('backup_originals')) {
                    // Backup doesn't add savings, but adds safety
                }
            });

            // Cap savings at reasonable maximum
            estimatedSavings = Math.min(estimatedSavings, 35);

            // Update summary display
            $('.webp-summary .enabled-count').text(enabledCount + ' optimizations enabled');
            $('.webp-summary .estimated-savings').text('Estimated savings: ' + estimatedSavings + '%');

            // Update summary styling based on enabled count
            var summaryStats = $('.webp-summary .summary-stats');
            summaryStats.removeClass('low-optimization medium-optimization high-optimization');

            if (enabledCount === 0) {
                summaryStats.addClass('low-optimization');
            } else if (enabledCount <= 2) {
                summaryStats.addClass('medium-optimization');
            } else {
                summaryStats.addClass('high-optimization');
            }
        },

        // Format file size
        formatFileSize: function(bytes) {
            if (bytes === 0) return '0 B';

            var k = 1024;
            var sizes = ['B', 'KB', 'MB', 'GB'];
            var i = Math.floor(Math.log(bytes) / Math.log(k));

            return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
        },

        // CRITICAL FIX: Update stats display
        updateStatsDisplay: function(stats) {
            console.log('📊 Updating stats display:', stats);

            // Update metric cards
            if (stats.total_images !== undefined) {
                $('.metric-value[data-metric="total_images"]').text(stats.total_images.toLocaleString());
            }
            if (stats.converted_images !== undefined) {
                $('.metric-value[data-metric="converted_images"]').text(stats.converted_images.toLocaleString());
            }
            if (stats.unconverted_images !== undefined) {
                $('.metric-value[data-metric="unconverted_images"]').text(stats.unconverted_images.toLocaleString());
            }
            if (stats.conversion_percentage !== undefined) {
                $('.metric-value[data-metric="conversion_percentage"]').text(stats.conversion_percentage + '%');
            }
            if (stats.total_savings !== undefined) {
                $('.metric-value[data-metric="total_savings"]').text(this.formatFileSize(stats.total_savings));
            }
            if (stats.savings_percentage !== undefined) {
                $('.metric-value[data-metric="savings_percentage"]').text(stats.savings_percentage + '%');
            }

            // Update analysis results
            if (stats.unconverted_images !== undefined) {
                $('#analysis-results .analysis-item strong').text(stats.unconverted_images.toLocaleString());
            }
        },

        // CRITICAL FIX: Update analysis results display
        updateAnalysisResults: function(data) {
            console.log('📊 Updating analysis results:', data);

            const $analysisResults = $('#analysis-results');
            if ($analysisResults.length) {
                const analysisHtml = `
                    <div class="analysis-summary">
                        <div class="analysis-item">
                            <strong>${data.unconverted_images.toLocaleString()}</strong>
                            images ready for WebP conversion
                        </div>
                        <div class="analysis-item" style="margin-top: 5px; font-size: 12px; color: #888;">
                            Estimated space savings: ${data.estimated_savings_percent}% (${this.formatFileSize(data.estimated_size_savings)})
                        </div>
                        <div class="analysis-item" style="margin-top: 5px; font-size: 12px; color: #888;">
                            Total size analyzed: ${this.formatFileSize(data.total_size)}
                        </div>
                    </div>
                `;
                $analysisResults.html(analysisHtml);
            }
        },

        // CRITICAL FIX: Show scan results modal
        showScanResults: function(data) {
            console.log('📋 Showing scan results modal');

            // Create or update scan results modal
            let modalHtml = `
                <div id="scan-results-modal" class="redco-modal" style="display: none;">
                    <div class="redco-modal-content" style="max-width: 800px;">
                        <div class="redco-modal-header">
                            <h2>Image Scan Results</h2>
                            <button type="button" class="redco-modal-close">&times;</button>
                        </div>
                        <div class="redco-modal-body">
                            <div class="scan-summary" style="margin-bottom: 20px; padding: 15px; background: #f8f9fa; border-radius: 4px;">
                                <div class="summary-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 15px;">
                                    <div class="summary-item">
                                        <div class="summary-label">Total Images</div>
                                        <div class="summary-value">${data.total_images.toLocaleString()}</div>
                                    </div>
                                    <div class="summary-item">
                                        <div class="summary-label">Converted</div>
                                        <div class="summary-value">${data.converted_images.toLocaleString()}</div>
                                    </div>
                                    <div class="summary-item">
                                        <div class="summary-label">Ready for WebP</div>
                                        <div class="summary-value">${data.unconverted_images.toLocaleString()}</div>
                                    </div>
                                    <div class="summary-item">
                                        <div class="summary-label">Potential Savings</div>
                                        <div class="summary-value">${this.formatFileSize(data.estimated_size_savings)}</div>
                                    </div>
                                </div>
                            </div>

                            <div class="scan-details">
                                <h3>Recent Images (Sample)</h3>
                                <div class="images-list" style="max-height: 300px; overflow-y: auto;">
                                    ${data.scan_results.map(image => `
                                        <div class="image-item" style="display: flex; justify-content: space-between; align-items: center; padding: 8px; border-bottom: 1px solid #eee;">
                                            <div class="image-info">
                                                <div class="image-title" style="font-weight: bold;">${image.title || 'Untitled'}</div>
                                                <div class="image-meta" style="font-size: 12px; color: #666;">
                                                    ${image.mime_type} • ${this.formatFileSize(image.file_size)}
                                                </div>
                                            </div>
                                            <div class="image-status">
                                                <span class="status-badge ${image.is_converted ? 'converted' : 'unconverted'}"
                                                      style="padding: 2px 8px; border-radius: 12px; font-size: 11px;
                                                             background: ${image.is_converted ? '#d4edda' : '#fff3cd'};
                                                             color: ${image.is_converted ? '#155724' : '#856404'};">
                                                    ${image.is_converted ? 'Converted' : 'Ready for WebP'}
                                                </span>
                                            </div>
                                        </div>
                                    `).join('')}
                                </div>
                            </div>
                        </div>
                        <div class="redco-modal-footer">
                            <button type="button" class="button button-primary" onclick="$('#scan-results-modal').hide();">Close</button>
                        </div>
                    </div>
                </div>
            `;

            // Remove existing modal and add new one
            $('#scan-results-modal').remove();
            $('body').append(modalHtml);

            // Show modal
            $('#scan-results-modal').show();

            // Bind close events
            $('#scan-results-modal .redco-modal-close, #scan-results-modal .redco-modal-overlay').on('click', function() {
                $('#scan-results-modal').hide();
            });
        },

        // CRITICAL FIX: Show analysis modal
        showAnalysisModal: function(data) {
            console.log('📊 Showing analysis modal');

            // Create analysis modal with detailed results
            let modalHtml = `
                <div id="analysis-modal" class="redco-modal" style="display: none;">
                    <div class="redco-modal-content" style="max-width: 700px;">
                        <div class="redco-modal-header">
                            <h2>WebP Conversion Analysis</h2>
                            <button type="button" class="redco-modal-close">&times;</button>
                        </div>
                        <div class="redco-modal-body">
                            <div class="analysis-overview" style="margin-bottom: 20px; padding: 15px; background: #f8f9fa; border-radius: 4px;">
                                <div class="overview-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(120px, 1fr)); gap: 15px;">
                                    <div class="overview-item">
                                        <div class="overview-label">Images Analyzed</div>
                                        <div class="overview-value">${data.processable_count.toLocaleString()}</div>
                                    </div>
                                    <div class="overview-item">
                                        <div class="overview-label">Original Size</div>
                                        <div class="overview-value">${this.formatFileSize(data.total_original_size)}</div>
                                    </div>
                                    <div class="overview-item">
                                        <div class="overview-label">Estimated Savings</div>
                                        <div class="overview-value">${this.formatFileSize(data.estimated_total_savings)}</div>
                                    </div>
                                    <div class="overview-item">
                                        <div class="overview-label">Savings %</div>
                                        <div class="overview-value">${data.savings_percentage}%</div>
                                    </div>
                                </div>
                            </div>

                            <div class="server-compatibility" style="margin-bottom: 20px; padding: 10px; border-radius: 4px;
                                 background: ${data.server_support ? '#d4edda' : '#f8d7da'};
                                 color: ${data.server_support ? '#155724' : '#721c24'};">
                                <strong>Server WebP Support:</strong>
                                ${data.server_support ? '✅ Supported' : '❌ Not Supported'}
                            </div>

                            <div class="analysis-details">
                                <h3>Sample Images Analysis</h3>
                                <div class="details-list" style="max-height: 250px; overflow-y: auto;">
                                    ${data.analysis_details.map(image => `
                                        <div class="detail-item" style="display: flex; justify-content: space-between; align-items: center; padding: 8px; border-bottom: 1px solid #eee;">
                                            <div class="detail-info">
                                                <div class="detail-title" style="font-weight: bold;">${image.title || 'Untitled'}</div>
                                                <div class="detail-meta" style="font-size: 12px; color: #666;">
                                                    ${image.mime_type}
                                                </div>
                                            </div>
                                            <div class="detail-savings">
                                                <div class="savings-amount" style="font-weight: bold; color: #4CAF50;">
                                                    -${this.formatFileSize(image.estimated_savings)}
                                                </div>
                                                <div class="savings-percent" style="font-size: 11px; color: #666;">
                                                    ${this.formatFileSize(image.file_size)} → ${this.formatFileSize(image.estimated_webp_size)}
                                                </div>
                                            </div>
                                        </div>
                                    `).join('')}
                                </div>
                            </div>
                        </div>
                        <div class="redco-modal-footer">
                            <button type="button" class="button button-primary" onclick="$('#analysis-modal').hide();">Close</button>
                        </div>
                    </div>
                </div>
            `;

            // Remove existing modal and add new one
            $('#analysis-modal').remove();
            $('body').append(modalHtml);

            // Show modal
            $('#analysis-modal').show();

            // Bind close events
            $('#analysis-modal .redco-modal-close').on('click', function() {
                $('#analysis-modal').hide();
            });
        },

        // CRITICAL FIX: Show test results modal
        showTestResultsModal: function(data) {
            console.log('🧪 Showing test results modal');

            // Create test results modal
            let modalHtml = `
                <div id="test-results-modal" class="redco-modal" style="display: none;">
                    <div class="redco-modal-content" style="max-width: 600px;">
                        <div class="redco-modal-header">
                            <h2>WebP Support Test Results</h2>
                            <button type="button" class="redco-modal-close">&times;</button>
                        </div>
                        <div class="redco-modal-body">
                            <div class="test-results">
                                <div class="test-item" style="margin-bottom: 15px; padding: 10px; border-radius: 4px; background: #f8f9fa;">
                                    <strong>Server WebP Support:</strong>
                                    <span style="color: ${data.server_support ? '#4CAF50' : '#f44336'};">
                                        ${data.server_support ? '✅ Supported' : '❌ Not Supported'}
                                    </span>
                                </div>

                                <div class="test-details" style="font-size: 14px; line-height: 1.6;">
                                    <p><strong>Test Summary:</strong></p>
                                    <ul style="margin-left: 20px;">
                                        <li>GD Library: ${data.gd_support ? '✅ Available' : '❌ Not Available'}</li>
                                        <li>WebP Functions: ${data.webp_functions ? '✅ Available' : '❌ Not Available'}</li>
                                        <li>Image Types: ${data.image_types ? '✅ WebP Supported' : '❌ WebP Not Supported'}</li>
                                    </ul>

                                    ${!data.server_support ? `
                                        <div style="margin-top: 15px; padding: 10px; background: #fff3cd; border-radius: 4px; color: #856404;">
                                            <strong>⚠️ WebP Not Supported</strong><br>
                                            Your server doesn't support WebP conversion. Please contact your hosting provider to enable GD library with WebP support.
                                        </div>
                                    ` : `
                                        <div style="margin-top: 15px; padding: 10px; background: #d4edda; border-radius: 4px; color: #155724;">
                                            <strong>✅ WebP Fully Supported</strong><br>
                                            Your server can convert images to WebP format for optimal performance.
                                        </div>
                                    `}
                                </div>
                            </div>
                        </div>
                        <div class="redco-modal-footer">
                            <button type="button" class="button button-primary" onclick="$('#test-results-modal').hide();">Close</button>
                        </div>
                    </div>
                </div>
            `;

            // Remove existing modal and add new one
            $('#test-results-modal').remove();
            $('body').append(modalHtml);

            // Show modal
            $('#test-results-modal').show();

            // Bind close events
            $('#test-results-modal .redco-modal-close').on('click', function() {
                $('#test-results-modal').hide();
            });
        },

        // CRITICAL FIX: Show toast notification
        showToast: function(message, type = 'info', duration = 5000) {
            // Use global toast system if available
            if (typeof showToast === 'function') {
                showToast(message, type, duration);
            } else if (typeof RedcoToast !== 'undefined') {
                RedcoToast[type](message, { duration: duration });
            } else {
                // Fallback to console and alert
                console.log(`🔔 Toast (${type}): ${message}`);
                if (type === 'error') {
                    alert('Error: ' + message);
                }
            }
        },

        // CRITICAL FIX: Update convert button state based on available images
        updateConvertButtonState: function() {
            console.log('🔄 Checking convertible images for button state...');

            // Check if redcoWebP is available
            if (typeof redcoWebP === 'undefined') {
                console.warn('⚠️ redcoWebP not available, cannot check button state');
                return;
            }

            const $headerButton = $('#header-bulk-convert-images');
            const $sidebarButton = $('#bulk-convert-images');
            const $buttonText = $('#convert-button-text');
            const $buttonSpinner = $('#convert-button-spinner');

            // Show loading state
            if ($buttonText.length) {
                $buttonText.text('Checking for images...');
            }
            if ($buttonSpinner.length) {
                $buttonSpinner.css('visibility', 'visible');
            }

            $.ajax({
                url: redcoWebP.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_webp_get_convertible_count',
                    nonce: redcoWebP.nonces.bulk_convert
                },
                success: (response) => {
                    console.log('✅ Convertible count response:', response);

                    if (response.success && response.data) {
                        const count = response.data.convertible_count || 0;
                        const hasImages = response.data.has_convertible_images || false;

                        // Update button states
                        if (hasImages && count > 0) {
                            // Enable buttons
                            $headerButton.prop('disabled', false);
                            $sidebarButton.prop('disabled', false);

                            // Update button text
                            if ($buttonText.length) {
                                $buttonText.text(`Convert ${count} images`);
                            }

                            console.log(`✅ Convert buttons enabled - ${count} images available`);
                        } else {
                            // Disable buttons
                            $headerButton.prop('disabled', true);
                            $sidebarButton.prop('disabled', true);

                            // Update button text
                            if ($buttonText.length) {
                                $buttonText.text('No images to convert');
                            }

                            console.log('ℹ️ Convert buttons disabled - no convertible images');
                        }

                        // Hide spinner
                        if ($buttonSpinner.length) {
                            $buttonSpinner.css('visibility', 'hidden');
                        }

                        // Update status message if container exists
                        const $statusContainer = $('#conversion-status-message');
                        const $statusText = $('#status-message-text');
                        if ($statusContainer.length && $statusText.length) {
                            if (hasImages && count > 0) {
                                $statusText.text(`${count} images ready for WebP conversion`);
                                $statusContainer.show();
                            } else {
                                $statusText.text('All images have been converted to WebP format');
                                $statusContainer.show();
                            }
                        }

                    } else {
                        console.error('❌ Failed to get convertible count:', response);
                        this.handleButtonStateError();
                    }
                },
                error: (xhr, status, error) => {
                    console.error('❌ AJAX error getting convertible count:', error);
                    this.handleButtonStateError();
                }
            });
        },

        // CRITICAL FIX: Refresh stats functionality
        refreshStats: function() {
            console.log('🔄 Refreshing WebP stats...');

            // Check if redcoWebP is available
            if (typeof redcoWebP === 'undefined') {
                console.error('❌ redcoWebP not available for stats refresh');
                this.showToast('Error: WebP system not initialized', 'error');
                return;
            }

            const $button = $('#header-refresh-stats');
            const originalText = $button.text();

            // Show loading state
            $button.prop('disabled', true).html('<span class="dashicons dashicons-update spin"></span> Refreshing...');

            $.ajax({
                url: redcoWebP.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_webp_refresh_stats'
                },
                success: (response) => {
                    console.log('✅ Stats refresh response:', response);

                    if (response.success) {
                        // Update stats display
                        this.updateStatsDisplay(response.data.stats);
                        this.showToast('Statistics refreshed successfully', 'success');

                        // Trigger stats update
                        this.refreshStats();
                    } else {
                        console.error('❌ Stats refresh failed:', response);
                        this.showToast('Failed to refresh stats: ' + (response.data || 'Unknown error'), 'error');
                    }
                },
                error: (xhr, status, error) => {
                    console.error('❌ AJAX error refreshing stats:', error);
                    this.showToast('Network error refreshing stats: ' + error, 'error');
                },
                complete: () => {
                    // Restore button state
                    $button.prop('disabled', false).text(originalText);
                }
            });
        },

        // CRITICAL FIX: Scan images functionality
        scanImages: function() {
            console.log('🔍 Scanning images for WebP conversion...');

            // Check if redcoWebP is available
            if (typeof redcoWebP === 'undefined') {
                console.error('❌ redcoWebP not available for image scanning');
                this.showToast('Error: WebP system not initialized', 'error');
                return;
            }

            const $button = $('#scan-images');
            const originalText = $button.text();

            // Show loading state
            $button.prop('disabled', true).html('<span class="dashicons dashicons-search spin"></span> Scanning...');

            $.ajax({
                url: redcoWebP.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_webp_scan_images'
                },
                success: (response) => {
                    console.log('✅ Image scan response:', response);

                    if (response.success) {
                        const data = response.data;

                        // Update analysis results display
                        this.updateAnalysisResults(data);

                        // Show detailed results
                        this.showScanResults(data);

                        this.showToast(`Scan complete: ${data.unconverted_images} images ready for conversion`, 'success');
                    } else {
                        console.error('❌ Image scan failed:', response);
                        this.showToast('Failed to scan images: ' + (response.data || 'Unknown error'), 'error');
                    }
                },
                error: (xhr, status, error) => {
                    console.error('❌ AJAX error scanning images:', error);
                    this.showToast('Network error scanning images: ' + error, 'error');
                },
                complete: () => {
                    // Restore button state
                    $button.prop('disabled', false).text(originalText);
                }
            });
        },

        // CRITICAL FIX: Analyze WebP potential functionality
        analyzeWebPPotential: function() {
            console.log('📊 Analyzing WebP conversion potential...');

            // Check if redcoWebP is available
            if (typeof redcoWebP === 'undefined') {
                console.error('❌ redcoWebP not available for WebP analysis');
                this.showToast('Error: WebP system not initialized', 'error');
                return;
            }

            const $button = $('#analyze-webp-potential');
            const originalText = $button.text();

            // Show loading state
            $button.prop('disabled', true).html('<span class="dashicons dashicons-analytics spin"></span> Analyzing...');

            $.ajax({
                url: redcoWebP.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_webp_analyze_webp_potential'
                },
                success: (response) => {
                    console.log('✅ WebP analysis response:', response);

                    if (response.success) {
                        const data = response.data;

                        // Show analysis modal with detailed results
                        this.showAnalysisModal(data);

                        this.showToast(`Analysis complete: ${data.processable_count} images analyzed`, 'success');
                    } else {
                        console.error('❌ WebP analysis failed:', response);
                        this.showToast('Failed to analyze WebP potential: ' + (response.data || 'Unknown error'), 'error');
                    }
                },
                error: (xhr, status, error) => {
                    console.error('❌ AJAX error analyzing WebP potential:', error);
                    this.showToast('Network error analyzing WebP potential: ' + error, 'error');
                },
                complete: () => {
                    // Restore button state
                    $button.prop('disabled', false).text(originalText);
                }
            });
        },

        // CRITICAL FIX: Test WebP support functionality
        testWebPSupport: function() {
            console.log('🧪 Testing WebP server support...');

            // Check if redcoWebP is available
            if (typeof redcoWebP === 'undefined') {
                console.error('❌ redcoWebP not available for WebP testing');
                this.showToast('Error: WebP system not initialized', 'error');
                return;
            }

            const $button = $('#test-webp-support');
            const originalText = $button.text();

            // Show loading state
            $button.prop('disabled', true).html('<span class="dashicons dashicons-admin-tools spin"></span> Testing...');

            $.ajax({
                url: redcoWebP.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_webp_test_conversion',
                    nonce: redcoWebP.nonces.test
                },
                success: (response) => {
                    console.log('✅ WebP test response:', response);

                    if (response.success) {
                        this.showToast('WebP support test completed successfully', 'success');

                        // Show test results modal
                        this.showTestResultsModal(response.data);
                    } else {
                        console.error('❌ WebP test failed:', response);
                        this.showToast('WebP support test failed: ' + (response.data || 'Unknown error'), 'error');
                    }
                },
                error: (xhr, status, error) => {
                    console.error('❌ AJAX error testing WebP support:', error);
                    this.showToast('Network error testing WebP support: ' + error, 'error');
                },
                complete: () => {
                    // Restore button state
                    $button.prop('disabled', false).text(originalText);
                }
            });
        },

        // Enhanced WebP Test Function
        testEnhancedWebP: function() {
            console.log('🚀 Starting Enhanced WebP Test...');

            const $button = $('#test-enhanced-webp');
            const originalText = $button.text();
            $button.prop('disabled', true).text('Testing...');

            $.ajax({
                url: redcoWebP.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_webp_enhanced_test',
                    nonce: redcoWebP.nonces.test
                },
                success: function(response) {
                    $button.prop('disabled', false).text(originalText);
                    console.log('Enhanced WebP Test Results:', response);

                    if (response.success) {
                        const data = response.data;

                        console.log('=== ENHANCED SERVER CAPABILITIES ===');
                        console.log('WebP Support:', data.capabilities.webp_support ? '✅ YES' : '❌ NO');
                        console.log('Memory Available:', data.capabilities.memory_available ? '✅ YES' : '❌ NO');
                        console.log('Write Permissions:', data.capabilities.write_permissions ? '✅ YES' : '❌ NO');
                        console.log('GD Info:', data.capabilities.gd_info);

                        if (data.test_conversion) {
                            console.log('');
                            console.log('=== ENHANCED TEST CONVERSION ===');
                            if (data.test_conversion.success) {
                                console.log('✅ Enhanced test conversion SUCCESSFUL!');
                                console.log('Image ID:', data.test_conversion.image_id);
                                console.log('Original File:', data.test_conversion.original_file);
                                console.log('WebP File:', data.test_conversion.webp_file);
                                console.log('Original Size:', data.test_conversion.original_size, 'bytes');
                                console.log('WebP Size:', data.test_conversion.webp_size, 'bytes');
                                console.log('Savings:', data.test_conversion.original_size - data.test_conversion.webp_size, 'bytes');

                                alert('✅ Enhanced WebP Test PASSED!\n\n' +
                                      'Server supports WebP conversion with enhanced features.\n' +
                                      'Test conversion successful!\n\n' +
                                      'Check console for detailed results.');
                            } else {
                                console.log('❌ Enhanced test conversion failed:', data.test_conversion.error);
                                alert('❌ Enhanced WebP Test FAILED!\n\n' +
                                      'Test conversion failed: ' + data.test_conversion.error + '\n\n' +
                                      'Check console for detailed results.');
                            }
                        }

                        if (data.logs && data.logs.length > 0) {
                            console.log('');
                            console.log('=== ENHANCED LOGS ===');
                            data.logs.forEach(log => console.log(log));
                        }

                    } else {
                        console.log('❌ Enhanced WebP test failed:', response.data);
                        alert('❌ Enhanced WebP Test FAILED!\n\n' + response.data);
                    }
                },
                error: function(xhr, status, error) {
                    $button.prop('disabled', false).text(originalText);
                    console.log('❌ Enhanced WebP Test AJAX Error:', error);
                    alert('❌ Enhanced WebP Test Error!\n\nAJAX request failed: ' + error);
                }
            });
        }
    };

    // Initialize when document is ready
    $(document).ready(function() {
        // Multiple detection methods for better reliability
        const isWebPPage = window.location.href.indexOf('tab=smart-webp-conversion') !== -1 ||
                          $('.redco-module-tab[data-module="smart-webp-conversion"]').length > 0 ||
                          $('#test-webp-modal').length > 0;

        if (isWebPPage) {
            WebPAdmin.init();
        }
    });

    // Make WebPAdmin globally available
    window.WebPAdmin = WebPAdmin;

    // Fallback initialization - instant retry if needed
    if (typeof window.WebPAdmin !== 'undefined' && !window.WebPAdmin.initialized) {
        if ($('#test-webp-modal').length > 0) {
            WebPAdmin.init();
            WebPAdmin.initialized = true;
        }
    }

})(jQuery);

/**
 * Enhanced WebP Module Features
 * Dynamic button states, recent conversions, and modern UI
 */
(function($) {
    'use strict';

    window.RedcoWebPEnhanced = {
        initialized: false,
        currentOffset: 0,
        currentLimit: 10,
        currentSort: 'date',

        // Initialize enhanced features
        init: function() {
            if (this.initialized) return;

            console.log('🚀 Initializing Enhanced WebP Features');

            this.initDynamicButtonState();
            this.initRecentConversions();
            this.initToastNotifications();
            this.bindEvents();

            // STATS FIX: Load initial stats on initialization
            if (typeof WebPAdmin !== 'undefined' && typeof WebPAdmin.updateStats === 'function') {
                WebPAdmin.updateStats();
            }

            this.initialized = true;
        },

        // Initialize dynamic button state management
        initDynamicButtonState: function() {
            console.log('🔄 Initializing dynamic button state...');
            this.checkConvertibleImages();
        },

        // Check for convertible images and update button state
        checkConvertibleImages: function() {
            const self = this;

            console.log('🔧 WEBP BUTTON STATE: Checking convertible images...');

            $.ajax({
                url: redcoWebP.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_webp_get_convertible_count',
                    nonce: redcoWebP.nonces.bulk_convert
                },
                success: function(response) {
                    console.log('🔧 WEBP BUTTON STATE: AJAX response received:', response);

                    if (response.success) {
                        console.log('🔧 WEBP BUTTON STATE: Success - updating button with data:', response.data);
                        self.updateButtonState(response.data);
                    } else {
                        console.error('🔧 WEBP BUTTON STATE: Failed to get convertible images count:', response.data);
                        self.updateButtonState({
                            convertible_count: 0,
                            has_convertible_images: false,
                            message: 'Unable to check images'
                        });
                    }
                },
                error: function(xhr, status, error) {
                    console.error('🔧 WEBP BUTTON STATE: AJAX error checking convertible images:', {
                        status: xhr.status,
                        statusText: xhr.statusText,
                        error: error,
                        responseText: xhr.responseText
                    });
                    self.updateButtonState({
                        convertible_count: 0,
                        has_convertible_images: false,
                        message: 'Connection error'
                    });
                }
            });
        },

        // Update button state based on convertible images
        updateButtonState: function(data) {
            console.log('🔧 WEBP BUTTON STATE: updateButtonState called with data:', data);

            const $button = $('#bulk-convert-images');
            const $headerButton = $('#header-bulk-convert-images');
            const $buttonText = $('#convert-button-text');
            const $spinner = $('#convert-button-spinner');
            const $statusMessage = $('#conversion-status-message');
            const $statusText = $('#status-message-text');

            console.log('🔧 WEBP BUTTON STATE: Found elements:', {
                button: $button.length,
                headerButton: $headerButton.length,
                buttonText: $buttonText.length,
                spinner: $spinner.length,
                statusMessage: $statusMessage.length,
                statusText: $statusText.length
            });

            // Hide spinner
            $spinner.hide();



            if (data.has_convertible_images) {
                // Enable main button
                $button.prop('disabled', false)
                       .removeClass('button-secondary testing-mode')
                       .addClass('button-primary')
                       .css('background', '')
                       .css('border-color', '');

                // Enable header button
                $headerButton.prop('disabled', false);

                $buttonText.text(`Convert ${data.convertible_count} Images`);

                // Show status message
                $statusMessage.show();
                $statusText.text(data.message);
                $statusMessage.css('border-left-color', '#4CAF50');
                $statusMessage.find('.dashicons').removeClass('dashicons-info').addClass('dashicons-yes-alt');

            } else {
                // Disable main button
                $button.prop('disabled', true)
                       .removeClass('button-primary testing-mode')
                       .addClass('button-secondary')
                       .css('background', '')
                       .css('border-color', '');

                // Disable header button
                $headerButton.prop('disabled', true);

                $buttonText.text('No Images to Convert');

                // Show status message
                $statusMessage.show();
                $statusText.text(data.message || 'All images have been converted to WebP format');
                $statusMessage.css('border-left-color', '#666');
                $statusMessage.find('.dashicons').removeClass('dashicons-yes-alt').addClass('dashicons-info');
            }

            console.log(`✅ Button state updated: ${data.convertible_count} convertible images`);
        },

        // Initialize recent conversions functionality
        initRecentConversions: function() {
            console.log('📋 Initializing recent conversions...');
            this.loadRecentConversions();
        },

        // CLEAN OVERHAUL: Load recent conversions via AJAX (max 10, no pagination)
        loadRecentConversions: function() {
            const self = this;

            $('#conversions-loading').show();
            $('#recent-conversions-list').hide();
            $('#conversions-empty').hide();

            $.ajax({
                url: redcoWebP.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_webp_get_recent_conversions',
                    limit: 10, // CLEAN OVERHAUL: Always limit to 10
                    offset: 0, // CLEAN OVERHAUL: Always start from 0
                    sort_by: this.currentSort,
                    nonce: redcoWebP.nonces.bulk_convert
                },
                success: function(response) {
                    $('#conversions-loading').hide();

                    if (response.success) {
                        self.renderConversions(response.data); // CLEAN OVERHAUL: No append parameter
                    } else {
                        console.error('Failed to load recent conversions:', response.data);
                        $('#conversions-empty').show();
                    }
                },
                error: function(xhr, status, error) {
                    $('#conversions-loading').hide();
                    $('#conversions-empty').show();
                    console.error('AJAX error loading conversions:', error);
                }
            });
        },

        // CLEAN OVERHAUL: Render conversions list (max 10, no pagination)
        renderConversions: function(data) {
            const $list = $('#recent-conversions-list');

            $list.empty(); // Always clear the list

            if (!data.conversions || data.conversions.length === 0) {
                $('#conversions-empty').show();
                return;
            }

            $list.show();
            $('#conversions-empty').hide();

            // CLEAN OVERHAUL: Render only first 10 conversions, no pagination
            const conversionsToShow = data.conversions.slice(0, 10);
            conversionsToShow.forEach(conversion => {
                const conversionHtml = this.renderConversionItem(conversion);
                $list.append(conversionHtml);
            });

            // CLEAN OVERHAUL: Hide all pagination elements
            $('#conversions-load-more').hide();
            $('#conversions-pagination-info').hide();

            console.log(`✅ Rendered ${conversionsToShow.length} conversions (max 10)`);
        },

        // CLEAN OVERHAUL: Render individual conversion item
        renderConversionItem: function(conversion) {
            return `
                <div class="recent-conversion-item">
                    <div class="conversion-main">
                        <div class="conversion-title">${conversion.title}</div>
                        <div class="conversion-meta">
                            <span class="original-size">${conversion.formatted_original_size}</span>
                            <span class="arrow">→</span>
                            <span class="webp-size">${conversion.formatted_webp_size}</span>
                            <span class="savings">${conversion.savings_percentage}% saved</span>
                            <span class="date">${conversion.formatted_date}</span>
                        </div>
                    </div>
                </div>
            `;
        },

        // Initialize toast notifications - REMOVED: Now uses global toast system
        initToastNotifications: function() {
            // WebP module now uses the global toast notification system
            // No custom toast initialization needed
        },

        // REMOVED: Auto-save interception - WebP module now uses global toast system

        // REMOVED: Global auto-save override - WebP module now uses standard global toast system

        // Bind events for enhanced features
        bindEvents: function() {
            const self = this;

            // Conversions sorting
            $('#conversions-sort').on('change', function() {
                self.currentSort = $(this).val();
                self.loadRecentConversions(); // CLEAN OVERHAUL: No offset needed
            });

            // CLEAN OVERHAUL: Removed Load More button handler (no longer needed)

            // REMOVED: Manual refresh stats button (no longer needed with simple stats)

            // Refresh convertible count after conversion
            $(document).on('webp-conversion-completed', function() {
                setTimeout(() => {
                    self.checkConvertibleImages();
                    self.loadRecentConversions();

                    // STATS FIX: Also refresh stats after conversion
                    if (typeof WebPAdmin !== 'undefined' && typeof WebPAdmin.updateStats === 'function') {
                        WebPAdmin.updateStats();
                    }
                }, 1000);
            });

            // REMOVED: Auto-convert checkbox should NOT trigger button state checks
            // The auto-convert setting is purely for new uploads, not existing images

            // Refresh button state periodically
            setInterval(() => {
                if ($('#bulk-convert-images').is(':visible')) {
                    self.checkConvertibleImages();
                }
            }, 30000); // Every 30 seconds


        }

        // REMOVED: showConversionDetails function (no longer needed)
    };

    // Initialize when document is ready
    $(document).ready(function() {
        // Check if we're on the WebP module page
        const isWebPPage = window.location.href.indexOf('tab=smart-webp-conversion') !== -1 ||
                          $('.redco-module-tab[data-module="smart-webp-conversion"]').length > 0;

        if (isWebPPage) {
            RedcoWebPEnhanced.init();
        }
    });

})(jQuery);

// Comprehensive WebP diagnostic function
window.testWebPDiagnostics = function() {
    console.log('🔧 Running Comprehensive WebP Diagnostics...');

    // Test 1: Check convertible images count
    jQuery.ajax({
        url: redcoWebP.ajaxurl,
        type: 'POST',
        data: {
            action: 'redco_webp_get_convertible_count',
            nonce: redcoWebP.nonces.bulk_convert
        },
        success: function(response) {
            console.log('📊 CONVERTIBLE COUNT:', response);

            // Test 2: Get WebP stats
            jQuery.ajax({
                url: redcoWebP.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_webp_get_stats',
                    nonce: redcoWebP.nonces.stats
                },
                success: function(statsResponse) {
                    console.log('📊 WEBP STATS:', statsResponse);

                    // Test 3: Debug image detection
                    jQuery.ajax({
                        url: redcoWebP.ajaxurl,
                        type: 'POST',
                        data: {
                            action: 'redco_webp_debug_images',
                            nonce: redcoWebP.nonces.stats
                        },
                        success: function(debugResponse) {
                            console.log('📊 IMAGE DEBUG:', debugResponse);

                            // Compile comprehensive report
                            const report = {
                                convertible_count: response.success ? response.data.convertible_count : 'Error',
                                total_images: debugResponse.success ? debugResponse.data.total_images : 'Error',
                                converted_images: (statsResponse.success || statsResponse.data) ?
                                    (statsResponse.data || statsResponse).total_converted || 0 : 'Error',
                                mime_types: debugResponse.success ? debugResponse.data.mime_types : 'Error'
                            };

                            console.log('📋 COMPREHENSIVE REPORT:', report);

                            if (typeof showToast === 'function') {
                                const message = `Images: ${report.total_images} total, ${report.converted_images} converted, ${report.convertible_count} convertible`;
                                showToast(message, 'info', 8000);
                            }

                            // Provide recommendations
                            if (report.total_images === 0) {
                                console.log('💡 RECOMMENDATION: Upload some JPEG/PNG images to Media Library');
                            } else if (report.convertible_count === 0 && report.converted_images > 0) {
                                console.log('💡 RECOMMENDATION: All images already converted to WebP');
                            } else if (report.convertible_count === 0) {
                                console.log('💡 RECOMMENDATION: Check if images are valid JPEG/PNG format');
                            }
                        },
                        error: function() {
                            console.log('📊 IMAGE DEBUG: Failed (endpoint may not exist)');
                        }
                    });
                },
                error: function() {
                    console.log('📊 WEBP STATS: Failed to get stats');
                }
            });
        },
        error: function(xhr, status, error) {
            console.error('📊 CONVERTIBLE COUNT: AJAX error', error);
        }
    });
};

// REMOVED: Debug stats testing function - not needed in production

// REMOVED: Debug media library test function - not needed in production

// Debug function to test convertible images count
window.testWebPConvertibleCount = function() {
    console.log('🔧 Testing WebP Convertible Images Count...');

    jQuery.ajax({
        url: redcoWebP.ajaxurl,
        type: 'POST',
        data: {
            action: 'redco_webp_get_convertible_count',
            nonce: redcoWebP.nonces.bulk_convert
        },
        success: function(response) {
            console.log('🔧 Convertible Count Response:', response);
            if (response.success) {
                const data = response.data;
                console.log('🔧 Convertible Count:', data.convertible_count);
                console.log('🔧 Has Convertible Images:', data.has_convertible_images);
                console.log('🔧 Message:', data.message);

                if (typeof showToast === 'function') {
                    showToast(`Found ${data.convertible_count} convertible images`, 'info', 5000);
                }
            } else {
                console.error('🔧 Failed to get convertible count:', response.data);
                if (typeof showToast === 'function') {
                    showToast('Failed to get convertible images count', 'error', 5000);
                }
            }
        },
        error: function(xhr, status, error) {
            console.error('🔧 AJAX error getting convertible count:', error);
            if (typeof showToast === 'function') {
                showToast('Network error while checking images', 'error', 5000);
            }
        }
    });
};

// Debug function to test auto-convert setting
window.testWebPAutoConvertSetting = function() {
    console.log('🔧 Testing WebP Auto-Convert Setting...');

    jQuery.ajax({
        url: redcoAjax.ajaxurl,
        type: 'POST',
        data: {
            action: 'redco_debug_get_option',
            option_name: 'redco_optimizer_smart_webp_conversion',
            nonce: redcoAjax.nonce
        },
        success: function(response) {
            if (response.success) {
                console.log('🔧 WebP Settings Debug:', response.data);
                if (response.data.option_value && response.data.option_value.auto_convert_uploads !== undefined) {
                    const value = response.data.option_value.auto_convert_uploads;
                    console.log('🔧 auto_convert_uploads value:', value);
                    console.log('🔧 auto_convert_uploads type:', typeof value);
                    console.log('🔧 Boolean evaluation:', !!value);

                    // Test the NEW robust logic used in the WebP module
                    let auto_convert_enabled = false;
                    if (typeof value === 'boolean') {
                        auto_convert_enabled = value;
                    } else if (typeof value === 'number') {
                        auto_convert_enabled = value === 1;
                    } else if (typeof value === 'string') {
                        auto_convert_enabled = ['1', 'true', 'on', 'yes'].includes(value.toLowerCase());
                    }

                    console.log('🔧 NEW Module logic result:', auto_convert_enabled);
                    console.log('🔧 Upload conversion will be:', auto_convert_enabled ? 'ENABLED' : 'DISABLED');

                    // Show user-friendly result
                    if (typeof showToast === 'function') {
                        const message = auto_convert_enabled ?
                            'Auto-convert is ENABLED - new uploads will be converted to WebP' :
                            'Auto-convert is DISABLED - new uploads will remain in original format';

                        const toastType = auto_convert_enabled ? 'info' : 'success';
                        showToast(message, toastType, 5000);
                    }
                } else {
                    console.warn('🔧 auto_convert_uploads setting not found in database');
                    if (typeof showToast === 'function') {
                        showToast('Auto-convert setting not found - defaults to DISABLED', 'warning', 5000);
                    }
                }
            } else {
                console.error('Failed to get WebP settings:', response.data);
                if (typeof showToast === 'function') {
                    showToast('Failed to retrieve WebP settings', 'error', 5000);
                }
            }
        },
        error: function() {
            console.error('AJAX error getting WebP settings');
            if (typeof showToast === 'function') {
                showToast('Network error while testing settings', 'error', 5000);
            }
        }
    });
};