/**
 * Smart WebP Conversion Admin JavaScript
 *
 * @package Redco_Optimizer
 * @subpackage Smart_WebP_Conversion
 * @since 1.0.0
 */

// Script loading verification
window.webpScriptLoaded = true;





(function($) {
    'use strict';

    var WebPAdmin = {

        // Properties
        isConverting: false,
        autoRefreshTimer: null,
        autoRefreshInterval: 45000, // 45 seconds
        isAutoRefreshEnabled: true,
        conversionData: {
            totalImages: 0,
            processedImages: 0,
            errorCount: 0,
            totalSavings: 0,
            currentBatch: 0,
            batchSize: 10
        },

        // Initialize
        init: function() {
            if (this.initialized) {
                return;
            }

            this.initProgressModal();
            this.bindEvents();
            // REMOVED: All automatic stats loading - only manual refresh button works
            this.initialized = true;
        },

        // Initialize progress modal
        initProgressModal: function() {
            const existingModal = $('#webp-progress-modal');

            // Create progress modal HTML if it doesn't exist
            if (existingModal.length === 0) {
                const modalHtml = `
                    <div id="webp-progress-modal" class="redco-modal" style="display: none;">
                        <div class="redco-modal-content compact-modal">
                            <div class="redco-modal-header">
                                <h3 id="webp-progress-modal-title">Converting Images to WebP</h3>
                            </div>
                            <div class="redco-modal-body">
                                <div class="progress-container">
                                    <div class="progress-bar">
                                        <div class="progress-fill" style="width: 0%"></div>
                                    </div>
                                    <div class="progress-stats left-aligned">
                                        <span class="progress-current">0</span> / <span class="progress-total">0</span> images processed
                                    </div>
                                </div>
                                <div class="conversion-stats single-row">
                                    <div class="stat-card">
                                        <span class="stat-label">Processed</span>
                                        <span class="stat-value processed-count">0</span>
                                    </div>
                                    <div class="stat-card">
                                        <span class="stat-label">Errors</span>
                                        <span class="stat-value error-count">0</span>
                                    </div>
                                    <div class="stat-card">
                                        <span class="stat-label">Savings</span>
                                        <span class="stat-value total-savings">0 KB</span>
                                    </div>
                                </div>
                                <div class="current-operation">
                                    <div class="current-file">Preparing conversion...</div>
                                </div>
                                <div class="conversion-log-section">
                                    <div class="log-toggle" id="log-toggle">
                                        <span class="dashicons dashicons-arrow-right-alt2"></span>
                                        <span class="log-toggle-text">Conversion Log</span>
                                        <span class="log-count">(0 entries)</span>
                                    </div>
                                    <div class="conversion-log collapsed" id="conversion-log">
                                        <div class="log-entries"></div>
                                    </div>
                                </div>
                            </div>
                            <div class="redco-modal-footer">
                                <button type="button" class="button button-secondary" id="cancel-conversion">Cancel</button>
                                <button type="button" class="button button-primary" id="close-conversion-modal" style="display: none;">Close</button>
                            </div>
                        </div>
                    </div>
                `;
                $('body').append(modalHtml);

                // Bind collapsible log toggle
                this.bindLogToggle();
            }
        },

        // Bind collapsible log toggle functionality
        bindLogToggle: function() {
            $(document).on('click', '#log-toggle', function() {
                const $toggle = $(this);
                const $log = $('#conversion-log');
                const $icon = $toggle.find('.dashicons');

                if ($log.hasClass('collapsed')) {
                    // Expand log
                    $log.removeClass('collapsed').addClass('expanded');
                    $icon.removeClass('dashicons-arrow-right-alt2').addClass('dashicons-arrow-down-alt2');
                } else {
                    // Collapse log
                    $log.removeClass('expanded').addClass('collapsed');
                    $icon.removeClass('dashicons-arrow-down-alt2').addClass('dashicons-arrow-right-alt2');
                }
            });
        },

        // Update log entry count
        updateLogCount: function() {
            const entryCount = $('.log-entries .log-entry').length;
            $('.log-count').text(`(${entryCount} entries)`);
        },

        // REMOVED: All auto-refresh functionality - only manual refresh button works

        // REMOVED: Auto-refresh indicator and quiet refresh functions

        // REMOVED: Page visibility handling for auto-refresh

        // Bind event handlers
        bindEvents: function() {
            // Handle manual refresh with convert button update
            $(document).on('click', '#header-refresh-stats', function(e) {
                e.preventDefault();
                WebPAdmin.refreshStats();

                // REMOVED: Automatic button state update - only manual refresh works
            });



            $(document).on('click', '#analyze-webp-potential', function(e) {
                e.preventDefault();
                WebPAdmin.analyzeWebPPotential();
            });

            $(document).on('click', '#test-webp-support', function(e) {
                e.preventDefault();
                WebPAdmin.testWebPSupport();
            });

            // Add missing bulk convert button handler with immediate modal display
            $(document).on('click', '#bulk-convert-images', function(e) {
                e.preventDefault();

                // Ensure modal shows immediately
                if (typeof WebPAdmin !== 'undefined') {
                    if (!WebPAdmin.initialized) {
                        WebPAdmin.init();
                    }
                    WebPAdmin.startBulkConversion();
                } else {
                    alert('Error: WebP conversion system not initialized. Please refresh the page.');
                }
            });

            // Test modal button
            $(document).on('click', '#test-webp-modal', function(e) {
                e.preventDefault();
                WebPAdmin.showModal();
            });

            // Debug image detection button
            $(document).on('click', '#debug-image-detection', function(e) {
                e.preventDefault();
                WebPAdmin.debugImageDetection();
            });

            // Simple AJAX test button
            $(document).on('click', '#test-simple-ajax', function(e) {
                e.preventDefault();
                WebPAdmin.testSimpleAjax();
            });

            // Test image detection button
            $(document).on('click', '#test-image-detection', function(e) {
                e.preventDefault();
                WebPAdmin.testImageDetection();
            });

            // Verify WebP files button
            $(document).on('click', '#verify-webp-files', function(e) {
                e.preventDefault();
                WebPAdmin.verifyWebPFiles();
            });

            // Reset WebP conversions button
            $(document).on('click', '#reset-webp-conversions', function(e) {
                e.preventDefault();
                if (confirm('Are you sure you want to reset all WebP conversion data? This will allow images to be re-converted with new settings.')) {
                    WebPAdmin.resetWebPConversions();
                }
            });

            // Reset specific images button
            $(document).on('click', '#reset-specific-images', function(e) {
                e.preventDefault();
                WebPAdmin.resetSpecificImages();
            });

            // Test WebP creation button
            $(document).on('click', '#test-webp-creation', function(e) {
                e.preventDefault();
                WebPAdmin.testWebPCreation();
            });

            // Check Image Status button
            $(document).on('click', '#check-image-status', function(e) {
                e.preventDefault();
                WebPAdmin.checkImageStatus();
            });

            // Enhanced WebP Test button
            $(document).on('click', '#test-enhanced-webp', function(e) {
                e.preventDefault();
                WebPAdmin.testEnhancedWebP();
            });

            // CRITICAL FIX: Remove duplicate handler - already handled by event delegation above
            // $('#test-webp-support').on('click', this.testServerSupport.bind(this));

            // Modal close buttons
            $('#close-conversion-modal, #cancel-conversion').on('click', this.closeModal.bind(this));

            // Quality slider - update display only, auto-save is handled globally
            $('#quality').on('input', function() {
                $('#quality-value').text($(this).val());
            });

            // Lossless toggle
            $('#lossless').on('change', function() {
                if ($(this).is(':checked')) {
                    $('#quality-setting').slideUp();
                } else {
                    $('#quality-setting').slideDown();
                }
            });

            // WebP checkbox change handlers - use event delegation to avoid conflicts with auto-save
            $(document).on('change', '.webp-checkbox', this.updateWebPSummary.bind(this));

            // REMOVED: All automatic timers - only manual refresh button works

            // REMOVED: Automatic summary initialization - only manual refresh button works
        },

        // REMOVED: initializeConvertButtonState function - only manual refresh button works

        // Start bulk conversion process
        startBulkConversion: function() {
            if (this.isConverting) {
                alert('Conversion already in progress!');
                return;
            }

            // Show modal instantly
            this.showModal();
            this.isConverting = true;

            // Get all processable images upfront before starting
            const batchSize = this.getBatchSizeFromSettings();

            $.ajax({
                url: redcoWebP.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_webp_get_processable_images',
                    nonce: redcoWebP.nonces.bulk_convert
                },
                success: (response) => {
                    if (response.success && response.data) {
                        const imageIds = response.data.image_ids;
                        const totalImages = response.data.total_images;



                        // Reset conversion data with pre-validated image list
                        this.conversionData = {
                            totalImages: totalImages,
                            processedImages: 0,
                            errorCount: 0,
                            totalSavings: 0,
                            currentBatch: 0,
                            batchSize: batchSize,
                            currentOffset: 0,
                            imageIds: imageIds // SIMPLE FIX: Store all image IDs upfront
                        };

                        // CRITICAL FIX: Add starting log entry
                        const logContainer = $('.log-entries');
                        if (logContainer.length > 0) {
                            const startEntry = $('<div class="log-entry log-info">')
                                .html(`<span class="dashicons dashicons-update"></span> ` +
                                      `<strong>Starting bulk conversion of ${totalImages} images...</strong>`);
                            logContainer.append(startEntry);
                            this.updateLogCount();
                        }

                        // Start processing with pre-validated list
                        this.processImageBatch(0);
                    } else {

                        this.handleError('Failed to get processable images: ' + (response.data || 'Unknown error'));
                    }
                },
                error: (xhr, status, error) => {

                    this.handleError('Failed to get processable images: ' + error);
                }
            });

            // Update modal title
            $('#webp-progress-modal-title').text('WebP Conversion');
            $('.current-file').text('Starting bulk conversion...');
        },

        // BATCH CALCULATION FIX: Get convertible images count before starting conversion
        getConvertibleImagesCount: function() {
            return new Promise((resolve, reject) => {
                $.ajax({
                    url: redcoWebP.ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'redco_webp_get_convertible_count',
                        nonce: redcoWebP.nonces.bulk_convert
                    },
                    success: function(response) {
                        if (response.success && response.data) {
                            const count = response.data.convertible_count || 0;
                            resolve(count);
                        } else {
                            reject(new Error('Failed to get convertible images count'));
                        }
                    },
                    error: function(xhr, status, error) {
                        reject(new Error('AJAX error: ' + error));
                    }
                });
            });
        },

        // FIXED: Get batch size from settings
        getBatchSizeFromSettings: function() {
            // Try to get from settings form first
            const settingsBatchSize = $('#batch_size').val();
            if (settingsBatchSize) {
                const size = parseInt(settingsBatchSize);
                if (size >= 5 && size <= 50) {
                    return size;
                }
            }

            // PERFORMANCE FIX: Increase default batch size for faster processing
            return 20;
        },

        // SIMPLE FIX: Process images from pre-validated list (no runtime checks)
        processImageBatch: function(offset) {
            const self = this;

            // Check if we have more images to process
            if (offset >= this.conversionData.imageIds.length) {
                this.completeConversion();
                return;
            }

            // Get batch of image IDs to process
            const batchSize = this.conversionData.batchSize;
            const imageBatch = this.conversionData.imageIds.slice(offset, offset + batchSize);
            const batchNumber = Math.floor(offset / batchSize) + 1;
            const totalBatches = Math.ceil(this.conversionData.imageIds.length / batchSize);

            // Update progress display
            $('.current-file').text(`Processing batch ${batchNumber}/${totalBatches}...`);

            // CRITICAL FIX: Add batch start log entry
            const logContainer = $('.log-entries');
            if (logContainer.length > 0) {
                const batchEntry = $('<div class="log-entry log-info">')
                    .html(`<span class="dashicons dashicons-admin-tools"></span> ` +
                          `<strong>Processing batch ${batchNumber}/${totalBatches}</strong><br>` +
                          `<small>Converting ${imageBatch.length} images...</small>`);
                logContainer.append(batchEntry);
                logContainer.scrollTop(logContainer[0].scrollHeight);
                this.updateLogCount();
            }

            $.ajax({
                url: redcoWebP.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_webp_bulk_convert',
                    batch_size: batchSize,
                    offset: offset,
                    image_ids: imageBatch, // SIMPLE FIX: Send specific image IDs to process
                    nonce: redcoWebP.nonces.bulk_convert
                },
                success: function(response) {
                    if (response.success && response.data) {
                        self.handleBatchSuccess(response.data, offset);

                        // Continue with next batch
                        const nextOffset = offset + batchSize;
                        if (nextOffset < self.conversionData.imageIds.length && self.isConverting) {
                            self.processImageBatch(nextOffset);
                        } else {
                            self.completeConversion();
                        }
                    } else {
                        console.error('❌ Batch processing failed:', response);
                        self.handleError('Batch processing failed: ' + (response.data || 'Unknown error'));
                    }
                },
                error: function(xhr, status, error) {
                    console.error('❌ AJAX error in batch processing:', error);
                    self.handleError('Network error during conversion: ' + error);
                }
            });
        },

        // SIMPLE FIX: Complete the conversion process
        completeConversion: function() {

            // Update progress to 100%
            $('.progress-fill').css('width', '100%');
            $('.progress-current').text(this.conversionData.totalImages);
            $('.progress-total').text(this.conversionData.totalImages);
            $('.current-file').text('Conversion completed successfully!');

            // Show completion message
            const processed = this.conversionData.processedImages;
            const total = this.conversionData.totalImages;
            const errors = this.conversionData.errorCount;

            $('.conversion-complete').show();
            $('.conversion-complete .conversion-summary').html(`
                ✅ Conversion Complete!<br>
                📸 Processed: ${processed} images<br>
                💾 Total Savings: ${this.formatFileSize(this.conversionData.totalSavings)}<br>
                ${errors > 0 ? `⚠️ Errors: ${errors}` : ''}
            `);

            // CRITICAL FIX: Switch from Cancel to Close button after completion
            $('#cancel-conversion').hide();
            $('#close-conversion-modal').show();

            // Update modal title to show completion
            $('#webp-progress-modal-title').text('WebP Conversion Complete');

            // CRITICAL FIX: Add completion log entry (was missing!)
            const logContainer = $('.log-entries');
            if (logContainer.length > 0) {
                const completionEntry = $('<div class="log-entry log-complete">')
                    .html(`<span class="dashicons dashicons-yes-alt"></span> ` +
                          `<strong>Bulk conversion completed successfully!</strong>`);
                logContainer.append(completionEntry);
                logContainer.scrollTop(logContainer[0].scrollHeight);

                // Update log count
                this.updateLogCount();
            }

            // Stop the conversion flag
            this.isConverting = false;

            // Refresh stats after completion
            this.refreshStats();

            // REMOVED: All automatic button state updates - only manual refresh button works
        },

        // SIMPLE FIX: Handle batch success for simple approach
        handleBatchSuccess: function(data, offset) {
            // Update conversion data with batch results
            this.conversionData.processedImages += data.processed || 0;
            this.conversionData.errorCount += (data.errors ? data.errors.length : 0);

            // Calculate total savings from conversions
            if (data.conversions && data.conversions.length > 0) {
                data.conversions.forEach((conversion) => {
                    this.conversionData.totalSavings += (conversion.savings || 0);
                });
            }

            // CRITICAL FIX: Add conversion log entries (was missing!)
            this.logEnhancedConversions(data.conversions || [], data.errors || []);

            // Log debug info if available
            if (data.debug_info && data.debug_info.length > 0) {
                this.logDebugInfo(data.debug_info);
            }

            // Update progress display
            const processed = this.conversionData.processedImages;
            const total = this.conversionData.totalImages;
            const percentage = total > 0 ? Math.round((processed / total) * 100) : 0;

            $('.progress-fill').css('width', percentage + '%');
            $('.progress-current').text(processed);
            $('.progress-total').text(total);
            $('.processed-count').text(processed);
            $('.error-count').text(this.conversionData.errorCount);
            $('.total-savings').text(this.formatFileSize(this.conversionData.totalSavings));

            // Progress updated successfully
        },

        // Process Enhanced Batch (now the main conversion method)
        processEnhancedBatch: function(offset) {
            const self = this;

            // CRITICAL FIX: Update current offset tracking
            this.conversionData.currentOffset = offset;

            // BATCH CALCULATION FIX: Calculate batch numbers correctly
            const batchNumber = Math.floor(offset / self.conversionData.batchSize) + 1;
            const totalImages = self.conversionData.totalImages || 0;
            const totalBatches = totalImages > 0 ? Math.ceil(totalImages / self.conversionData.batchSize) : 1;

            // REAL-TIME FEEDBACK: Show detailed progress messages with correct batch info
            const progressMessages = [
                `🔍 Scanning batch ${batchNumber}/${totalBatches} for convertible images...`,
                `📊 Analyzing image formats and sizes...`,
                `🚀 Starting WebP conversion process...`,
                `⚡ Processing ${self.conversionData.batchSize} images in parallel...`,
                `🔧 Optimizing image quality and compression...`,
                `💾 Saving converted WebP files...`,
                `📈 Calculating file size savings...`,
                `🔄 Preparing next batch...`
            ];

            // Starting batch processing

            let messageIndex = 0;
            $('.current-file').text(progressMessages[messageIndex]);

            // Update progress message every 800ms to show activity
            const progressInterval = setInterval(() => {
                messageIndex = (messageIndex + 1) % progressMessages.length;
                $('.current-file').text(progressMessages[messageIndex]);
            }, 800);

            $.ajax({
                url: redcoWebP.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_webp_bulk_convert',
                    batch_size: self.conversionData.batchSize,
                    offset: offset,
                    nonce: redcoWebP.nonces.bulk_convert
                },
                success: function(response) {
                    // REAL-TIME FEEDBACK: Clear progress interval and show result
                    clearInterval(progressInterval);

                    if (response.success) {
                        $('.current-file').text(`✅ Batch ${batchNumber} completed successfully!`);
                        self.handleEnhancedBatchSuccess(response);
                    } else {
                        // Enhanced error handling with specific error codes
                        let errorMsg = 'Conversion failed';
                        let debugInfo = '';

                        if (response.data) {
                            if (response.data.message) {
                                errorMsg = response.data.message;
                            }

                            if (response.data.error_code) {
                                switch (response.data.error_code) {
                                    case 'NONCE_FAILED':
                                        errorMsg = 'Security verification failed. Please refresh the page and try again.';
                                        break;
                                    case 'INSUFFICIENT_PERMISSIONS':
                                        errorMsg = 'You do not have sufficient permissions to perform this action.';
                                        break;
                                    case 'MODULE_DISABLED':
                                        errorMsg = 'WebP conversion module is not enabled. Please enable it in the Modules page.';
                                        break;
                                    case 'WEBP_NOT_SUPPORTED':
                                        errorMsg = 'Your server does not support WebP conversion. Please contact your hosting provider.';
                                        break;
                                    case 'INVALID_BATCH_SIZE':
                                        errorMsg = 'Invalid batch size. Please refresh the page and try again.';
                                        break;
                                }
                            }

                            if (response.data.debug_info) {
                                debugInfo = response.data.debug_info;
                                console.error('WebP Conversion Debug Info:', debugInfo);
                            }
                        }

                        self.handleError(errorMsg);
                    }
                },
                error: function(xhr, status, error) {
                    let errorMsg = 'Network error during conversion';
                    let debugInfo = '';

                    // Enhanced network error handling
                    if (xhr.status === 400) {
                        errorMsg = 'Bad request. Please check your settings and try again.';
                        debugInfo = 'HTTP 400: The request was malformed or invalid.';
                    } else if (xhr.status === 403) {
                        errorMsg = 'Access denied. Please check your permissions.';
                        debugInfo = 'HTTP 403: Insufficient permissions for this action.';
                    } else if (xhr.status === 404) {
                        errorMsg = 'Service not found. Please refresh the page and try again.';
                        debugInfo = 'HTTP 404: AJAX endpoint not found.';
                    } else if (xhr.status === 500) {
                        errorMsg = 'Server error. Please try again or contact support.';
                        debugInfo = 'HTTP 500: Internal server error occurred.';
                    } else if (xhr.status === 0) {
                        errorMsg = 'Connection failed. Please check your internet connection.';
                        debugInfo = 'Network connection failed or request was aborted.';
                    } else {
                        errorMsg = `Network error (${xhr.status}). Please try again.`;
                        debugInfo = `HTTP ${xhr.status}: ${xhr.statusText}`;
                    }

                    console.error('WebP Conversion Network Error:', {
                        status: xhr.status,
                        statusText: xhr.statusText,
                        responseText: xhr.responseText,
                        error: error,
                        debugInfo: debugInfo
                    });

                    // REAL-TIME FEEDBACK: Clear progress interval on error
                    clearInterval(progressInterval);
                    $('.current-file').text('❌ ' + errorMsg);

                    self.handleError(errorMsg);
                }
            });
        },

        // Handle successful enhanced batch conversion
        handleEnhancedBatchSuccess: function(response) {
            const data = response.data;

            // Update conversion data with real results
            this.conversionData.processedImages = data.total_processed;
            this.conversionData.errorCount += (data.errors ? data.errors.length : 0);

            // Calculate total savings from real conversions
            const batchSavings = (data.conversions || []).reduce((total, conversion) => {
                return total + (conversion.savings || 0);
            }, 0);
            this.conversionData.totalSavings += batchSavings;

            // Update progress with real data
            this.updateEnhancedProgress(data);

            // Log real conversions and errors
            this.logEnhancedConversions(data.conversions || [], data.errors || []);

            // Log debug info if available
            if (data.debug_info && data.debug_info.length > 0) {
                this.logDebugInfo(data.debug_info);
            }

            // Continue with next batch if there are more images
            if (data.has_more && this.isConverting) {

                // BATCH CALCULATION FIX: Show accurate batch transition message
                const totalImages = this.conversionData.totalImages || data.total_images || data.total_processed;
                const batchSize = this.conversionData.batchSize || 10;
                const nextBatchNumber = Math.floor(data.total_processed / batchSize) + 1;
                const totalBatches = Math.ceil(totalImages / batchSize);

                $('.current-file').text(`🔄 Preparing batch ${nextBatchNumber}/${totalBatches}... (${data.total_processed}/${totalImages} images processed)`);

                // CRITICAL FIX: Use the server's calculated next offset
                const nextOffset = data.next_offset || (this.conversionData.currentOffset + batchSize);
                this.conversionData.currentOffset = nextOffset;

                // Continue to next batch

                // PERFORMANCE FIX: Remove 1.5 second delay for faster processing
                this.processEnhancedBatch(nextOffset);
            } else {
                // COMPLETION LOGIC: Calculate final statistics
                const totalImages = this.conversionData.totalImages || data.total_images;
                const processed = data.total_processed;
                const remaining = totalImages - processed;

                // Check for incomplete conversion (silent)

                this.completeEnhancedConversion();
            }
        },

        // Update enhanced progress display
        updateEnhancedProgress: function(data) {
            const processed = data.total_processed;

            // CRITICAL FIX: Always use the initially set totalImages, never update it from server responses
            // The server's get_convertible_images_count() can return different values as images are converted
            const total = this.conversionData.totalImages || data.total_images || processed;

            // CRITICAL FIX: Only set totalImages once at the beginning, never update it during conversion
            if (data.total_images && !this.conversionData.totalImages) {
                this.conversionData.totalImages = data.total_images;
            }

            // CRITICAL DEBUG: Log if server is trying to change the total
            if (data.total_images && this.conversionData.totalImages && data.total_images !== this.conversionData.totalImages) {
                console.warn('⚠️ BATCH CALCULATION: Server returned different total_images!', {
                    stored: this.conversionData.totalImages,
                    server: data.total_images,
                    processed: processed
                });
                console.warn('⚠️ BATCH CALCULATION: Keeping original total to maintain consistency');
            }

            // BATCH CALCULATION FIX: Calculate current batch number correctly
            const batchSize = this.conversionData.batchSize || 10;
            const currentBatch = Math.floor(processed / batchSize) + 1;
            const totalBatches = Math.ceil(total / batchSize);

            // Update progress bar with real data
            const percentage = total > 0 ? Math.round((processed / total) * 100) : 0;
            $('.progress-fill').css('width', percentage + '%');

            // Update progress text with real numbers
            $('.progress-current').text(processed);
            $('.progress-total').text(total);

            // Update stats with real data
            $('.processed-count').text(processed);
            $('.error-count').text(this.conversionData.errorCount);
            $('.total-savings').text(this.formatFileSize(this.conversionData.totalSavings));

            // BATCH CALCULATION FIX: Update progress display
            // Batch processing completed successfully

            // REAL-TIME FEEDBACK: Update current operation and progress messages
            if (data.current_operation) {
                $('.current-file').text(data.current_operation);
            } else if (data.current_file) {
                $('.current-file').text(`Processing: ${data.current_file}`);
            }

            // Show latest progress message if available
            if (data.progress_messages && data.progress_messages.length > 0) {
                const latestMessage = data.progress_messages[data.progress_messages.length - 1];
                $('.current-file').text(latestMessage);
            }
        },

        // Log enhanced conversion results with detailed information
        logEnhancedConversions: function(conversions, errors) {
            const logContainer = $('.log-entries');

            // Safety check for log container
            if (logContainer.length === 0) {
                return;
            }

            // Log successful conversions with detailed info
            conversions.forEach((conversion) => {
                const logEntry = $('<div class="log-entry log-success">')
                    .html(`<span class="dashicons dashicons-yes"></span> ` +
                          `<strong>${conversion.title}</strong><br>` +
                          `<small>Original: ${this.formatFileSize(conversion.original_size)} → ` +
                          `WebP: ${this.formatFileSize(conversion.webp_size)} ` +
                          `(${conversion.savings_percentage}% saved)</small>`);
                logContainer.append(logEntry);
            });

            // Log errors with detailed info
            errors.forEach((error) => {
                const logEntry = $('<div class="log-entry log-error">')
                    .html(`<span class="dashicons dashicons-no"></span> ` +
                          `<strong>${error.title}</strong><br>` +
                          `<small>Error: ${error.error}</small>`);
                logContainer.append(logEntry);
            });

            // Scroll to bottom (with safety check)
            if (logContainer[0]) {
                logContainer.scrollTop(logContainer[0].scrollHeight);
            }

            // Update log count
            this.updateLogCount();
        },

        // Log debug information
        logDebugInfo: function(debugInfo) {
            const logContainer = $('.log-entries');
            if (logContainer.length === 0) return;

            debugInfo.forEach((info) => {
                const logEntry = $('<div class="log-entry log-debug">')
                    .html(`<span class="dashicons dashicons-info"></span> ` +
                          `<small>${info}</small>`);
                logContainer.append(logEntry);
            });

            // Scroll to bottom
            if (logContainer[0]) {
                logContainer.scrollTop(logContainer[0].scrollHeight);
            }

            // Update log count
            this.updateLogCount();
        },

        // Complete enhanced conversion process
        completeEnhancedConversion: function() {
            this.isConverting = false;

            // CRITICAL FIX: Switch from Cancel to Close button after completion
            $('#cancel-conversion').hide();
            $('#close-conversion-modal').show();

            // Update modal title to show completion
            $('#webp-progress-modal-title').text('WebP Conversion Complete');

            // CRITICAL: Update all stats and UI elements with fresh data
            this.refreshStats();

            // Show detailed completion message
            const processed = this.conversionData.processedImages;
            const errors = this.conversionData.errorCount;
            const savings = this.conversionData.totalSavings;

            let message = `<strong>✅ Conversion Complete!</strong><br>`;
            message += `📊 Processed: ${processed} images<br>`;

            if (savings > 0) {
                message += `💾 Total Savings: ${this.formatFileSize(savings)}<br>`;
            }

            if (errors > 0) {
                message += `⚠️ Errors: ${errors} images failed<br>`;
            }

            message += `<br><em>Check the log above for detailed results.</em>`;

            $('.current-file').html(message);

            // Add completion log entry
            const logContainer = $('.log-entries');
            if (logContainer.length > 0) {
                const completionEntry = $('<div class="log-entry log-complete">')
                    .html(`<span class="dashicons dashicons-yes-alt"></span> ` +
                          `<strong>Bulk conversion completed successfully!</strong>`);
                logContainer.append(completionEntry);
                logContainer.scrollTop(logContainer[0].scrollHeight);

                // Update log count
                this.updateLogCount();
            }

            // Trigger completion event for enhanced features
            $(document).trigger('webp-conversion-completed', {
                summary: {
                    total_processed: processed,
                    successful: processed - errors,
                    failed: errors,
                    total_savings: this.formatFileSize(savings),
                    average_savings: this.conversionData.averageSavings || '0%'
                },
                timestamp: new Date().toISOString()
            });

            // Show toast notification
            if (typeof showToast === 'function') {
                showToast(`Successfully converted ${processed} images!`, 'success', 8000);
            }
        },

        // Test server support
        testServerSupport: function() {
            var button = $('#test-webp-support');
            var originalText = button.text();

            button.prop('disabled', true).text('Testing...');

            $.ajax({
                url: redcoWebP.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_webp_test_conversion',
                    nonce: redcoWebP.nonces.test
                },
                success: function(response) {
                    button.prop('disabled', false).text(originalText);

                    if (response.success) {
                        alert('✓ ' + response.message);
                    } else {
                        alert('✗ ' + response.message);
                    }

                    // Server capabilities tested
                },
                error: function() {
                    button.prop('disabled', false).text(originalText);
                    alert('Error testing server support');
                }
            });
        },

        // REMOVED: updateStats function - only manual refresh button works now

        // Update convert button state based on available images
        updateConvertButtonState: function() {
            $.ajax({
                url: redcoWebP.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_webp_get_convertible_count',
                    nonce: redcoWebP.nonces.bulk_convert
                },
                success: function(response) {
                    if (response.success && response.data) {
                        const count = response.data.convertible_count || 0;
                        const $button = $('#bulk-convert-images');
                        const $buttonText = $('#convert-button-text');
                        const $spinner = $('#convert-button-spinner');

                        // Hide spinner
                        $spinner.hide();

                        if (count > 0) {
                            $button.prop('disabled', false);
                            $buttonText.text(`Convert ${count} Images`);
                        } else {
                            $button.prop('disabled', true);
                            $buttonText.text('No Images to Convert');
                        }
                    }
                },
                error: function(xhr, status, error) {
                    // Keep button in safe state
                    $('#bulk-convert-images').prop('disabled', true);
                    $('#convert-button-text').text('Error Checking Images');
                    $('#convert-button-spinner').hide();
                }
            });
        },

        // Load recent conversions list
        loadRecentConversions: function() {
            const $loadingDiv = $('#conversions-loading');
            const $listDiv = $('#recent-conversions-list');
            const $emptyDiv = $('#conversions-empty');

            // Show loading state
            $loadingDiv.show();
            $listDiv.hide();
            $emptyDiv.hide();

            $.ajax({
                url: redcoWebP.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_webp_get_recent_conversions',
                    limit: 10, // ENHANCED FIX: Show maximum 10 recent conversions
                    nonce: redcoWebP.nonces.stats // CRITICAL FIX: Ensure correct nonce for recent conversions
                },
                success: function(response) {
                    $loadingDiv.hide();

                    if (response.success && response.data && response.data.conversions && response.data.conversions.length > 0) {
                        // CLEAN OVERHAUL: Simple, clean conversion list
                        let html = '';
                        response.data.conversions.slice(0, 10).forEach(function(conversion) { // Limit to exactly 10 records
                            html += `
                                <div class="recent-conversion-item">
                                    <div class="conversion-main">
                                        <div class="conversion-title">${conversion.title}</div>
                                        <div class="conversion-meta">
                                            <span class="original-size">${conversion.formatted_original_size}</span>
                                            <span class="arrow">→</span>
                                            <span class="webp-size">${conversion.formatted_webp_size}</span>
                                            <span class="savings">${conversion.savings_percentage}% saved</span>
                                            <span class="date">${conversion.formatted_date}</span>
                                        </div>
                                    </div>
                                </div>
                            `;
                        });

                        $listDiv.html(html).show();
                    } else {
                        $emptyDiv.show();
                    }
                },
                error: function(xhr, status, error) {
                    $loadingDiv.hide();
                    $emptyDiv.show();
                    console.error('❌ Recent conversions AJAX error:', {
                        status: xhr.status,
                        statusText: xhr.statusText,
                        responseText: xhr.responseText,
                        error: error
                    });
                }
            });
        },

        // REMOVED: Auto-refresh check function - no longer needed

        // Enhanced refresh stats with real-time database queries and comprehensive UI updates
        refreshStats: function() {
            console.log('🚨 REFRESH STATS FUNCTION CALLED');
            // Get refresh button and show loading state (try multiple possible IDs)
            const $refreshButton = $('#header-refresh-stats, #refresh-webp-stats').first();
            const originalText = $refreshButton.text();
            $refreshButton.prop('disabled', true).text('Refreshing...');

            // Add visual feedback to all stat elements
            $('.stat-value, .header-metric-value').addClass('updating');

            $.ajax({
                url: redcoWebP.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_webp_refresh_stats',
                    nonce: redcoWebP.nonces.stats
                },
                success: function(response) {
                    // DEBUG: Use alert to see if we get here
                    alert('AJAX Success! Recent conversions count: ' +
                          (response.data && response.data.recent_conversions ? response.data.recent_conversions.length : 'NONE'));

                    if (response.success && response.data && response.data.stats) {
                        const data = response.data.stats;

                        // 1. Update Sidebar Stats with real-time data
                        $('.stat-item.stat-total-files .stat-value').text((data.total_images || 0).toLocaleString());
                        $('.stat-item.stat-converted-files .stat-value').text((data.converted_images || 0).toLocaleString());
                        $('.stat-item.stat-total-savings .stat-value').text(WebPAdmin.formatFileSize(data.total_savings || 0));
                        $('.stat-item.stat-avg-savings .stat-value').text((data.savings_percentage || 0) + '%');

                        // 2. Update Header Metrics with calculated values
                        $('.header-metric').eq(0).find('.header-metric-value').text((data.converted_images || 0).toLocaleString());
                        $('.header-metric').eq(1).find('.header-metric-value').text((data.conversion_percentage || 0) + '%');
                        $('.header-metric').eq(2).find('.header-metric-value').text(WebPAdmin.formatFileSize(data.total_savings || 0));

                        // 3. Update convert button state with convertible count
                        if (data.convertible_count !== undefined) {
                            const $convertButton = $('#bulk-convert-images');
                            const $convertButtonText = $('#convert-button-text');
                            const $statusMessage = $('#conversion-status-message');
                            const $statusText = $('#status-message-text');

                            if (data.convertible_count > 0) {
                                $convertButton.prop('disabled', false).show();
                                $convertButtonText.text(`Convert ${data.convertible_count} Images`);
                                $statusMessage.show();
                                $statusText.text(`${data.convertible_count} images ready for WebP conversion`);
                                $statusMessage.css('border-left-color', '#4CAF50');
                            } else {
                                $convertButton.prop('disabled', true);
                                $convertButtonText.text('No Images to Convert');
                                $statusMessage.show();
                                $statusText.text('All images have been converted to WebP format');
                                $statusMessage.css('border-left-color', '#666');
                            }
                        }

                        // 4. Add visual feedback for updated values
                        $('.stat-value, .header-metric-value').removeClass('updating').addClass('updated');
                        setTimeout(function() {
                            $('.stat-value, .header-metric-value').removeClass('updated');
                        }, 2000);

                        // 5. Update recent conversions if provided
                        console.log('🔍 DEBUGGING: Full response:', response);
                        console.log('🔍 DEBUGGING: response.data:', response.data);
                        console.log('🔍 DEBUGGING: recent_conversions:', response.data.recent_conversions);

                        if (response.data.recent_conversions) {
                            console.log('✅ DEBUGGING: Calling updateRecentConversions');
                            WebPAdmin.updateRecentConversions(response.data.recent_conversions);
                        } else {
                            console.log('❌ DEBUGGING: No recent_conversions found');
                        }

                        // 6. Show success notification
                        if (typeof showToast === 'function') {
                            showToast('Statistics refreshed successfully!', 'success', 3000);
                        }

                    } else {
                        if (typeof showToast === 'function') {
                            showToast('Failed to refresh stats: ' + (response.data || 'Unknown error'), 'error');
                        }
                    }
                },
                error: function(xhr, status, error) {
                    console.log('🚨 AJAX ERROR:', {
                        xhr: xhr,
                        status: status,
                        error: error,
                        responseText: xhr.responseText
                    });
                    if (typeof showToast === 'function') {
                        showToast('Network error refreshing stats', 'error');
                    }
                    $('.stat-value, .header-metric-value').removeClass('updating');
                },
                complete: function() {
                    // Restore button state
                    $refreshButton.prop('disabled', false).text(originalText);
                }
            });
        },

        // Update recent conversions display
        updateRecentConversions: function(conversions) {
            console.log('🔄 DEBUGGING: updateRecentConversions called');
            console.log('🔄 DEBUGGING: conversions parameter:', conversions);
            console.log('🔄 DEBUGGING: conversions length:', conversions ? conversions.length : 'null/undefined');

            const $loadingDiv = $('#conversions-loading');
            const $listDiv = $('#recent-conversions-list');
            const $emptyDiv = $('#conversions-empty');

            console.log('🔄 DEBUGGING: DOM elements:', {
                loading: $loadingDiv.length,
                list: $listDiv.length,
                empty: $emptyDiv.length
            });

            $loadingDiv.hide();

            if (!conversions || conversions.length === 0) {
                console.log('❌ DEBUGGING: No conversions, showing empty state');
                $listDiv.hide();
                $emptyDiv.show();
                return;
            }

            // Use the exact same format as the existing loadRecentConversions function
            let html = '';
            conversions.slice(0, 10).forEach(function(conversion) { // Limit to exactly 10 records
                html += `
                    <div class="recent-conversion-item">
                        <div class="conversion-main">
                            <div class="conversion-title">${conversion.title}</div>
                            <div class="conversion-meta">
                                <span class="original-size">${conversion.formatted_original_size}</span>
                                <span class="arrow">→</span>
                                <span class="webp-size">${conversion.formatted_webp_size}</span>
                                <span class="savings">${conversion.savings_percentage}% saved</span>
                                <span class="date">${conversion.formatted_date}</span>
                            </div>
                        </div>
                    </div>
                `;
            });

            $listDiv.html(html).show();
            $emptyDiv.hide();
        },

        // Format time ago (simple implementation)
        formatTimeAgo: function(timestamp) {
            const now = new Date().getTime() / 1000;
            const diff = now - timestamp;

            if (diff < 3600) {
                const minutes = Math.floor(diff / 60);
                return minutes + ' minutes ago';
            } else if (diff < 86400) {
                const hours = Math.floor(diff / 3600);
                return hours + ' hours ago';
            } else {
                const days = Math.floor(diff / 86400);
                return days + ' days ago';
            }
        },

        // Show conversion modal (INSTANT LOAD - no delays)
        showModal: function() {


            // Check if jQuery is available
            if (typeof $ === 'undefined') {
                alert('Error: jQuery is not loaded. Please refresh the page.');
                return;
            }

            // Ensure modal exists first
            this.initProgressModal();

            const $modal = $('#webp-progress-modal');

            if ($modal.length === 0) {
                alert('Modal creation failed! Check console for details.');
                return;
            }

            $('#webp-progress-modal-title').text('Converting Images to WebP');
            $modal.show();

            // CRITICAL FIX: Ensure correct button states at start
            $('#cancel-conversion').show(); // Show Cancel during conversion
            $('#close-conversion-modal').hide(); // Hide Close until completion

            // Reset modal content
            $('.progress-fill').css('width', '0%');
            $('.progress-current').text('0');
            $('.progress-total').text('0');
            $('.processed-count').text('0');
            $('.error-count').text('0');
            $('.total-savings').text('0 KB');
            $('.log-entries').empty();
            $('.current-file').text('Preparing conversion...');

            // Reset log section to collapsed state
            $('#conversion-log').removeClass('expanded').addClass('collapsed');
            $('#log-toggle .dashicons').removeClass('dashicons-arrow-down-alt2').addClass('dashicons-arrow-right-alt2');
            $('.log-count').text('(0 entries)');

            // Show/hide buttons
            $('#cancel-conversion').show();

            // Modal displayed
        },

        // Debug image detection
        debugImageDetection: function() {
            console.log('🔧 Testing image detection...');

            $.ajax({
                url: redcoWebP.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_webp_debug_images',
                    nonce: redcoWebP.nonce || redcoWebP.nonces.stats
                },
                success: function(response) {
                    if (response.success) {
                        alert('Debug Results:\n' +
                              'Total Images: ' + response.data.total_images + '\n' +
                              'Image IDs: ' + response.data.image_ids.join(', ') + '\n' +
                              'MIME Types: ' + response.data.mime_types.join(', '));
                    } else {
                        alert('Debug failed: ' + response.data);
                    }
                },
                error: function(xhr, status, error) {

                    alert('Debug request failed: ' + xhr.status + ' ' + xhr.statusText);
                }
            });
        },

        // Test simple AJAX (no nonce required)
        testSimpleAjax: function() {
            console.log('🔧 Testing simple AJAX...');

            $.ajax({
                url: redcoWebP.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_webp_test_simple'
                },
                success: function(response) {
                    console.log('✅ Simple AJAX response:', response);
                    alert('Simple AJAX Test Result:\n' + response.data.message);
                },
                error: function(xhr, status, error) {
                    console.error('❌ Simple AJAX failed:', {
                        status: xhr.status,
                        statusText: xhr.statusText,
                        responseText: xhr.responseText,
                        error: error
                    });
                    alert('Simple AJAX failed: ' + xhr.status + ' ' + xhr.statusText);
                }
            });
        },

        // Verify WebP files exist on disk
        verifyWebPFiles: function() {
            console.log('🔧 Verifying WebP files on disk...');

            $.ajax({
                url: redcoWebP.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_webp_verify_files',
                    nonce: redcoWebP.nonces.stats
                },
                success: function(response) {
                    console.log('✅ WebP file verification response:', response);
                    if (response.success) {
                        const data = response.data;
                        let message = 'WebP File Verification Results:\n\n';
                        message += 'Database Records: ' + data.database_records + '\n';
                        message += 'Files Found on Disk: ' + data.files_found + '\n';
                        message += 'Missing Files: ' + data.missing_files + '\n';
                        message += 'Empty Files: ' + data.empty_files + '\n\n';

                        if (data.missing_file_list && data.missing_file_list.length > 0) {
                            message += 'Missing Files:\n';
                            data.missing_file_list.forEach(function(file) {
                                message += '- ' + file + '\n';
                            });
                        }

                        alert(message);
                    } else {
                        alert('Verification failed: ' + response.data);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('❌ WebP verification failed:', xhr.status, xhr.statusText);
                    alert('Verification request failed: ' + xhr.status + ' ' + xhr.statusText);
                }
            });
        },

        // Reset WebP conversion data
        resetWebPConversions: function() {
            console.log('🔧 Resetting WebP conversion data...');

            $.ajax({
                url: redcoWebP.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_webp_reset_conversions',
                    nonce: redcoWebP.nonces.stats
                },
                success: function(response) {
                    console.log('✅ WebP reset response:', response);
                    if (response.success) {
                        const data = response.data;
                        let message = 'WebP Conversion Reset Results:\n\n';
                        message += 'Database Records Cleared: ' + data.records_cleared + '\n';
                        message += 'WebP Files Deleted: ' + data.files_deleted + '\n';
                        message += 'Conversion Log Cleared: ' + (data.log_cleared ? 'Yes' : 'No') + '\n\n';
                        message += 'You can now re-convert images with new settings.';

                        alert(message);

                        // Refresh the page to update statistics
                        location.reload();
                    } else {
                        alert('Reset failed: ' + response.data);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('❌ WebP reset failed:', xhr.status, xhr.statusText);
                    alert('Reset request failed: ' + xhr.status + ' ' + xhr.statusText);
                }
            });
        },

        // Test WebP creation with actual file
        testWebPCreation: function() {
            console.log('🔧 Testing WebP creation with actual file...');

            $.ajax({
                url: redcoWebP.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_webp_test_creation',
                    nonce: redcoWebP.nonces.stats
                },
                success: function(response) {
                    console.log('✅ WebP creation test response:', response);
                    if (response.success) {
                        const data = response.data;
                        let message = 'WebP Creation Test Results:\n\n';
                        message += 'Test Image Found: ' + (data.test_image_found ? 'Yes' : 'No') + '\n';
                        message += 'Test Image Path: ' + (data.test_image_path || 'N/A') + '\n';
                        message += 'WebP Support: ' + (data.webp_support ? 'Yes' : 'No') + '\n';
                        message += 'Directory Writable: ' + (data.directory_writable ? 'Yes' : 'No') + '\n';
                        message += 'Conversion Attempted: ' + (data.conversion_attempted ? 'Yes' : 'No') + '\n';
                        message += 'imagewebp() Success: ' + (data.imagewebp_success ? 'Yes' : 'No') + '\n';
                        message += 'File Created: ' + (data.file_created ? 'Yes' : 'No') + '\n';
                        message += 'File Size: ' + (data.file_size || 'N/A') + '\n';
                        message += 'Valid WebP: ' + (data.valid_webp ? 'Yes' : 'No') + '\n\n';

                        if (data.error_details) {
                            message += 'Error Details:\n' + data.error_details + '\n\n';
                        }

                        if (data.php_errors && data.php_errors.length > 0) {
                            message += 'PHP Errors:\n';
                            data.php_errors.forEach(function(error) {
                                message += '- ' + error + '\n';
                            });
                        }

                        alert(message);
                    } else {
                        alert('WebP creation test failed: ' + response.data);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('❌ WebP creation test failed:', xhr.status, xhr.statusText);
                    alert('WebP creation test request failed: ' + xhr.status + ' ' + xhr.statusText);
                }
            });
        },

        // Reset specific images based on filenames
        resetSpecificImages: function() {
            console.log('🔧 Resetting specific images...');

            // The 3 problematic images from the log
            const imageFilenames = [
                '133913847558663271-scaled.jpg',
                '133921112396416462-scaled.jpg',
                'login-logo.jpg'
            ];

            const confirmMessage = 'Reset conversion data for these 3 images?\n\n' +
                                 imageFilenames.join('\n') +
                                 '\n\nThis will allow them to be re-converted with proper settings.';

            if (!confirm(confirmMessage)) {
                return;
            }

            $.ajax({
                url: redcoWebP.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_webp_reset_specific_images',
                    nonce: redcoWebP.nonces.stats,
                    filenames: imageFilenames
                },
                success: function(response) {
                    console.log('✅ Specific reset response:', response);
                    if (response.success) {
                        const data = response.data;
                        let message = 'Specific Images Reset Results:\n\n';
                        message += 'Images Found: ' + data.images_found + '\n';
                        message += 'Records Cleared: ' + data.records_cleared + '\n';
                        message += 'WebP Files Deleted: ' + data.files_deleted + '\n\n';

                        if (data.processed_images && data.processed_images.length > 0) {
                            message += 'Processed Images:\n';
                            data.processed_images.forEach(function(img) {
                                message += '- ' + img + '\n';
                            });
                        }

                        message += '\nThese images can now be re-converted.';

                        alert(message);

                        // Refresh the page to update statistics
                        location.reload();
                    } else {
                        alert('Specific reset failed: ' + response.data);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('❌ Specific reset failed:', xhr.status, xhr.statusText);
                    alert('Specific reset request failed: ' + xhr.status + ' ' + xhr.statusText);
                }
            });
        },

        // Test image detection query
        testImageDetection: function() {
            console.log('🔧 Testing image detection query...');

            $.ajax({
                url: redcoWebP.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_webp_test_image_detection'
                },
                success: function(response) {
                    console.log('✅ Image detection test response:', response);
                    if (response.success) {
                        const data = response.data;
                        let message = 'Image Detection Test Results:\n\n';
                        message += 'Total Images: ' + data.all_images_count + '\n';
                        message += 'Unconverted Images: ' + data.unconverted_images_count + '\n';
                        message += 'Existing Conversions: ' + data.existing_conversions_count + '\n\n';

                        if (data.all_images_count > 0) {
                            message += 'All Images:\n';
                            data.all_images.forEach(function(img, index) {
                                message += (index + 1) + '. ID: ' + img.ID + ' - ' + img.post_mime_type + ' - ' + (img.post_title || 'No title') + '\n';
                            });
                        }

                        if (data.unconverted_images_count > 0) {
                            message += '\nUnconverted Images:\n';
                            data.unconverted_images.forEach(function(img, index) {
                                message += (index + 1) + '. ID: ' + img.ID + ' - ' + img.post_mime_type + ' - ' + (img.post_title || 'No title') + '\n';
                            });
                        }

                        alert(message);
                    } else {
                        alert('Image detection test failed: ' + response.data);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('❌ Image detection test failed:', xhr.status, xhr.statusText);
                    alert('Image detection test failed: ' + xhr.status + ' ' + xhr.statusText);
                }
            });
        },

        // Check image status and conversion data
        checkImageStatus: function() {
            console.log('🔧 Checking image status...');

            $.ajax({
                url: redcoWebP.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_webp_debug_images',
                    nonce: redcoWebP.nonce || redcoWebP.nonces.stats
                },
                success: function(response) {
                    console.log('✅ Image status response:', response);
                    if (response.success) {
                        const data = response.data;
                        let message = '📊 IMAGE STATUS REPORT\n\n';
                        message += '📁 Total Attachments: ' + data.total_attachments + '\n';
                        message += '🖼️ Total Images: ' + data.total_images + '\n';
                        message += '🆔 Image IDs: ' + data.image_ids.join(', ') + '\n\n';

                        message += '📋 MIME Types Found:\n';
                        data.all_attachment_types.forEach(function(type) {
                            message += '  • ' + type + '\n';
                        });

                        message += '\n🔍 Image MIME Types:\n';
                        data.mime_types.forEach(function(type, index) {
                            message += '  • ID ' + data.image_ids[index] + ': ' + type + '\n';
                        });

                        alert(message);
                    } else {
                        alert('❌ Image status check failed: ' + response.data);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('❌ Image status check failed:', xhr.status, xhr.statusText);
                    alert('❌ Image status check failed: ' + xhr.status + ' ' + xhr.statusText);
                }
            });
        },

        // Close modal
        closeModal: function() {
            if (this.isConverting) {
                if (confirm('Are you sure you want to cancel the conversion?')) {
                    this.isConverting = false;
                    $('#webp-progress-modal').hide();
                }
            } else {
                $('#webp-progress-modal').hide();
            }
        },

        // Enhanced error handling with actionable feedback
        handleError: function(message) {
            this.isConverting = false;

            // Update modal title to indicate error
            $('#webp-progress-modal-title').text('WebP Conversion Error');

            // Create enhanced error message with actionable feedback
            let errorHtml = '<div style="color: #d63638; padding: 15px; background: #fef7f7; border: 1px solid #f5c6cb; border-radius: 4px;">';
            errorHtml += '<strong>❌ Conversion Failed</strong><br><br>';
            errorHtml += '<div style="margin-bottom: 10px;">' + message + '</div>';

            // Add actionable suggestions based on error type
            if (message.includes('Security verification failed') || message.includes('nonce')) {
                errorHtml += '<div style="margin-top: 10px; padding: 10px; background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 3px;">';
                errorHtml += '<strong>💡 Solution:</strong> Please refresh the page and try again.';
                errorHtml += '</div>';
            } else if (message.includes('permissions') || message.includes('Access denied')) {
                errorHtml += '<div style="margin-top: 10px; padding: 10px; background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 3px;">';
                errorHtml += '<strong>💡 Solution:</strong> Please contact your administrator to check user permissions.';
                errorHtml += '</div>';
            } else if (message.includes('module is not enabled') || message.includes('MODULE_DISABLED')) {
                errorHtml += '<div style="margin-top: 10px; padding: 10px; background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 3px;">';
                errorHtml += '<strong>💡 Solution:</strong> Go to the Modules page and enable the WebP conversion module.';
                errorHtml += '</div>';
            } else if (message.includes('server does not support') || message.includes('WebP')) {
                errorHtml += '<div style="margin-top: 10px; padding: 10px; background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 3px;">';
                errorHtml += '<strong>💡 Solution:</strong> Contact your hosting provider to enable GD library with WebP support.';
                errorHtml += '</div>';
            } else if (message.includes('Network error') || message.includes('Connection failed')) {
                errorHtml += '<div style="margin-top: 10px; padding: 10px; background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 3px;">';
                errorHtml += '<strong>💡 Solution:</strong> Check your internet connection and try again.';
                errorHtml += '</div>';
            } else {
                errorHtml += '<div style="margin-top: 10px; padding: 10px; background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 3px;">';
                errorHtml += '<strong>💡 Suggestions:</strong><br>';
                errorHtml += '• Refresh the page and try again<br>';
                errorHtml += '• Check if the WebP module is enabled<br>';
                errorHtml += '• Verify your server supports WebP conversion<br>';
                errorHtml += '• Contact support if the problem persists';
                errorHtml += '</div>';
            }

            errorHtml += '</div>';

            $('.current-file').html(errorHtml);

            // Add error to log
            const logContainer = $('.log-entries');
            if (logContainer.length > 0) {
                const errorEntry = $('<div class="log-entry log-error">')
                    .html('<span class="dashicons dashicons-warning"></span> <strong>Conversion stopped due to error:</strong><br>' +
                          '<small>' + message + '</small>');
                logContainer.append(errorEntry);
                logContainer.scrollTop(logContainer[0].scrollHeight);

                // Update log count
                this.updateLogCount();
            }

            // Update buttons
            $('#cancel-conversion').hide();
            $('#close-conversion-modal').show();

            // Log error for debugging
            console.error('WebP Conversion Error:', message);
        },

        // Update WebP settings summary
        updateWebPSummary: function() {
            var enabledCount = 0;
            var estimatedSavings = 0;

            // Count enabled optimizations
            $('.webp-checkbox:checked').each(function() {
                enabledCount++;

                // Calculate estimated savings based on option
                var optionName = $(this).attr('name');
                if (optionName && optionName.includes('auto_convert_uploads')) {
                    estimatedSavings += 30; // 30% average savings for new uploads
                } else if (optionName && optionName.includes('replace_in_content')) {
                    estimatedSavings += 25; // 25% average savings for content replacement
                } else if (optionName && optionName.includes('backup_originals')) {
                    // Backup doesn't add savings, but adds safety
                }
            });

            // Cap savings at reasonable maximum
            estimatedSavings = Math.min(estimatedSavings, 35);

            // Update summary display
            $('.webp-summary .enabled-count').text(enabledCount + ' optimizations enabled');
            $('.webp-summary .estimated-savings').text('Estimated savings: ' + estimatedSavings + '%');

            // Update summary styling based on enabled count
            var summaryStats = $('.webp-summary .summary-stats');
            summaryStats.removeClass('low-optimization medium-optimization high-optimization');

            if (enabledCount === 0) {
                summaryStats.addClass('low-optimization');
            } else if (enabledCount <= 2) {
                summaryStats.addClass('medium-optimization');
            } else {
                summaryStats.addClass('high-optimization');
            }
        },

        // Format file size
        formatFileSize: function(bytes) {
            if (bytes === 0) return '0 B';

            var k = 1024;
            var sizes = ['B', 'KB', 'MB', 'GB'];
            var i = Math.floor(Math.log(bytes) / Math.log(k));

            return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
        },

        // Update stats display
        updateStatsDisplay: function(stats) {
            // Update sidebar stats using the actual HTML structure
            if (stats.total_images !== undefined) {
                $('.stat-total-files .stat-value').text(stats.total_images.toLocaleString());
            }
            if (stats.converted_images !== undefined) {
                $('.stat-converted-files .stat-value').text(stats.converted_images.toLocaleString());
            }
            if (stats.total_savings !== undefined) {
                $('.stat-total-savings .stat-value').text(this.formatFileSize(stats.total_savings));
            }
            if (stats.savings_percentage !== undefined) {
                $('.stat-avg-savings .stat-value').text(stats.savings_percentage + '%');
            }

            // Update header metrics
            if (stats.converted_images !== undefined) {
                $('.header-metric:first-child .header-metric-value').text(stats.converted_images.toLocaleString());
            }
            if (stats.conversion_percentage !== undefined) {
                $('.header-metric:nth-child(2) .header-metric-value').text(stats.conversion_percentage + '%');
            }
            if (stats.total_savings !== undefined) {
                $('.header-metric:nth-child(3) .header-metric-value').text(this.formatFileSize(stats.total_savings));
            }

            // Update analysis results
            if (stats.unconverted_images !== undefined) {
                $('#analysis-results .analysis-item strong').text(stats.unconverted_images.toLocaleString());
            }
        },

        // Update analysis results display
        updateAnalysisResults: function(data) {

            const $analysisResults = $('#analysis-results');
            if ($analysisResults.length) {
                const analysisHtml = `
                    <div class="analysis-summary">
                        <div class="analysis-item">
                            <strong>${data.unconverted_images.toLocaleString()}</strong>
                            images ready for WebP conversion
                        </div>
                        <div class="analysis-item" style="margin-top: 5px; font-size: 12px; color: #888;">
                            Estimated space savings: ${data.estimated_savings_percent}% (${this.formatFileSize(data.estimated_size_savings)})
                        </div>
                        <div class="analysis-item" style="margin-top: 5px; font-size: 12px; color: #888;">
                            Total size analyzed: ${this.formatFileSize(data.total_size)}
                        </div>
                    </div>
                `;
                $analysisResults.html(analysisHtml);
            }
        },

        // Show scan results modal
        showScanResults: function(data) {

            // Create or update scan results modal
            let modalHtml = `
                <div id="scan-results-modal" class="redco-modal" style="display: none;">
                    <div class="redco-modal-content" style="max-width: 800px;">
                        <div class="redco-modal-header">
                            <h2>Image Scan Results</h2>
                            <button type="button" class="redco-modal-close">&times;</button>
                        </div>
                        <div class="redco-modal-body">
                            <div class="scan-summary" style="margin-bottom: 20px; padding: 15px; background: #f8f9fa; border-radius: 4px;">
                                <div class="summary-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 15px;">
                                    <div class="summary-item">
                                        <div class="summary-label">Total Images</div>
                                        <div class="summary-value">${data.total_images.toLocaleString()}</div>
                                    </div>
                                    <div class="summary-item">
                                        <div class="summary-label">Converted</div>
                                        <div class="summary-value">${data.converted_images.toLocaleString()}</div>
                                    </div>
                                    <div class="summary-item">
                                        <div class="summary-label">Ready for WebP</div>
                                        <div class="summary-value">${data.unconverted_images.toLocaleString()}</div>
                                    </div>
                                    <div class="summary-item">
                                        <div class="summary-label">Potential Savings</div>
                                        <div class="summary-value">${this.formatFileSize(data.estimated_size_savings)}</div>
                                    </div>
                                </div>
                            </div>

                            <div class="scan-details">
                                <h3>Recent Images (Sample)</h3>
                                <div class="images-list" style="max-height: 300px; overflow-y: auto;">
                                    ${data.scan_results.map(image => `
                                        <div class="image-item" style="display: flex; justify-content: space-between; align-items: center; padding: 8px; border-bottom: 1px solid #eee;">
                                            <div class="image-info">
                                                <div class="image-title" style="font-weight: bold;">${image.title || 'Untitled'}</div>
                                                <div class="image-meta" style="font-size: 12px; color: #666;">
                                                    ${image.mime_type} • ${this.formatFileSize(image.file_size)}
                                                </div>
                                            </div>
                                            <div class="image-status">
                                                <span class="status-badge ${image.is_converted ? 'converted' : 'unconverted'}"
                                                      style="padding: 2px 8px; border-radius: 12px; font-size: 11px;
                                                             background: ${image.is_converted ? '#d4edda' : '#fff3cd'};
                                                             color: ${image.is_converted ? '#155724' : '#856404'};">
                                                    ${image.is_converted ? 'Converted' : 'Ready for WebP'}
                                                </span>
                                            </div>
                                        </div>
                                    `).join('')}
                                </div>
                            </div>
                        </div>
                        <div class="redco-modal-footer">
                            <button type="button" class="button button-primary" onclick="$('#scan-results-modal').hide();">Close</button>
                        </div>
                    </div>
                </div>
            `;

            // Remove existing modal and add new one
            $('#scan-results-modal').remove();
            $('body').append(modalHtml);

            // Show modal
            $('#scan-results-modal').show();

            // Bind close events
            $('#scan-results-modal .redco-modal-close, #scan-results-modal .redco-modal-overlay').on('click', function() {
                $('#scan-results-modal').hide();
            });
        },

        // Show analysis modal
        showAnalysisModal: function(data) {

            // Create analysis modal with detailed results
            let modalHtml = `
                <div id="analysis-modal" class="redco-modal" style="display: none;">
                    <div class="redco-modal-content" style="max-width: 700px;">
                        <div class="redco-modal-header">
                            <h2>WebP Conversion Analysis</h2>
                            <button type="button" class="redco-modal-close">&times;</button>
                        </div>
                        <div class="redco-modal-body">
                            <div class="analysis-overview" style="margin-bottom: 20px; padding: 15px; background: #f8f9fa; border-radius: 4px;">
                                <div class="overview-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(120px, 1fr)); gap: 15px;">
                                    <div class="overview-item">
                                        <div class="overview-label">Images Analyzed</div>
                                        <div class="overview-value">${data.processable_count.toLocaleString()}</div>
                                    </div>
                                    <div class="overview-item">
                                        <div class="overview-label">Original Size</div>
                                        <div class="overview-value">${this.formatFileSize(data.total_original_size)}</div>
                                    </div>
                                    <div class="overview-item">
                                        <div class="overview-label">Estimated Savings</div>
                                        <div class="overview-value">${this.formatFileSize(data.estimated_total_savings)}</div>
                                    </div>
                                    <div class="overview-item">
                                        <div class="overview-label">Savings %</div>
                                        <div class="overview-value">${data.savings_percentage}%</div>
                                    </div>
                                </div>
                            </div>

                            <div class="server-compatibility" style="margin-bottom: 20px; padding: 10px; border-radius: 4px;
                                 background: ${data.server_support ? '#d4edda' : '#f8d7da'};
                                 color: ${data.server_support ? '#155724' : '#721c24'};">
                                <strong>Server WebP Support:</strong>
                                ${data.server_support ? '✅ Supported' : '❌ Not Supported'}
                            </div>

                            <div class="analysis-details">
                                <h3>Sample Images Analysis</h3>
                                <div class="details-list" style="max-height: 250px; overflow-y: auto;">
                                    ${data.analysis_details.map(image => `
                                        <div class="detail-item" style="display: flex; justify-content: space-between; align-items: center; padding: 8px; border-bottom: 1px solid #eee;">
                                            <div class="detail-info">
                                                <div class="detail-title" style="font-weight: bold;">${image.title || 'Untitled'}</div>
                                                <div class="detail-meta" style="font-size: 12px; color: #666;">
                                                    ${image.mime_type}
                                                </div>
                                            </div>
                                            <div class="detail-savings">
                                                <div class="savings-amount" style="font-weight: bold; color: #4CAF50;">
                                                    -${this.formatFileSize(image.estimated_savings)}
                                                </div>
                                                <div class="savings-percent" style="font-size: 11px; color: #666;">
                                                    ${this.formatFileSize(image.file_size)} → ${this.formatFileSize(image.estimated_webp_size)}
                                                </div>
                                            </div>
                                        </div>
                                    `).join('')}
                                </div>
                            </div>
                        </div>
                        <div class="redco-modal-footer">
                            <button type="button" class="button button-primary" onclick="$('#analysis-modal').hide();">Close</button>
                        </div>
                    </div>
                </div>
            `;

            // Remove existing modal and add new one
            $('#analysis-modal').remove();
            $('body').append(modalHtml);

            // Show modal
            $('#analysis-modal').show();

            // Bind close events
            $('#analysis-modal .redco-modal-close').on('click', function() {
                $('#analysis-modal').hide();
            });
        },

        // Show test results modal
        showTestResultsModal: function(data) {

            // Create test results modal
            let modalHtml = `
                <div id="test-results-modal" class="redco-modal" style="display: none;">
                    <div class="redco-modal-content" style="max-width: 600px;">
                        <div class="redco-modal-header">
                            <h2>WebP Support Test Results</h2>
                            <button type="button" class="redco-modal-close">&times;</button>
                        </div>
                        <div class="redco-modal-body">
                            <div class="test-results">
                                <div class="test-item" style="margin-bottom: 15px; padding: 10px; border-radius: 4px; background: #f8f9fa;">
                                    <strong>Server WebP Support:</strong>
                                    <span style="color: ${data.server_support ? '#4CAF50' : '#f44336'};">
                                        ${data.server_support ? '✅ Supported' : '❌ Not Supported'}
                                    </span>
                                </div>

                                <div class="test-details" style="font-size: 14px; line-height: 1.6;">
                                    <p><strong>Test Summary:</strong></p>
                                    <ul style="margin-left: 20px;">
                                        <li>GD Library: ${data.gd_support ? '✅ Available' : '❌ Not Available'}</li>
                                        <li>WebP Functions: ${data.webp_functions ? '✅ Available' : '❌ Not Available'}</li>
                                        <li>Image Types: ${data.image_types ? '✅ WebP Supported' : '❌ WebP Not Supported'}</li>
                                    </ul>

                                    ${!data.server_support ? `
                                        <div style="margin-top: 15px; padding: 10px; background: #fff3cd; border-radius: 4px; color: #856404;">
                                            <strong>⚠️ WebP Not Supported</strong><br>
                                            Your server doesn't support WebP conversion. Please contact your hosting provider to enable GD library with WebP support.
                                        </div>
                                    ` : `
                                        <div style="margin-top: 15px; padding: 10px; background: #d4edda; border-radius: 4px; color: #155724;">
                                            <strong>✅ WebP Fully Supported</strong><br>
                                            Your server can convert images to WebP format for optimal performance.
                                        </div>
                                    `}
                                </div>
                            </div>
                        </div>
                        <div class="redco-modal-footer">
                            <button type="button" class="button button-primary" onclick="$('#test-results-modal').hide();">Close</button>
                        </div>
                    </div>
                </div>
            `;

            // Remove existing modal and add new one
            $('#test-results-modal').remove();
            $('body').append(modalHtml);

            // Show modal
            $('#test-results-modal').show();

            // Bind close events
            $('#test-results-modal .redco-modal-close').on('click', function() {
                $('#test-results-modal').hide();
            });
        },

        // CRITICAL FIX: Show toast notification
        showToast: function(message, type = 'info', duration = 5000) {
            // Use global toast system if available
            if (typeof showToast === 'function') {
                showToast(message, type, duration);
            } else if (typeof RedcoToast !== 'undefined') {
                RedcoToast[type](message, { duration: duration });
            } else {
                // Fallback to console and alert
                console.log(`🔔 Toast (${type}): ${message}`);
                if (type === 'error') {
                    alert('Error: ' + message);
                }
            }
        },

        // CRITICAL FIX: Update convert button state based on available images
        updateConvertButtonState: function() {

            // Check if redcoWebP is available
            if (typeof redcoWebP === 'undefined') {
                console.warn('⚠️ redcoWebP not available, cannot check button state');
                return;
            }

            const $headerButton = $('#header-bulk-convert-images');
            const $sidebarButton = $('#bulk-convert-images');
            const $buttonText = $('#convert-button-text');
            const $buttonSpinner = $('#convert-button-spinner');

            // Show loading state
            if ($buttonText.length) {
                $buttonText.text('Checking for images...');
            }
            if ($buttonSpinner.length) {
                $buttonSpinner.css('visibility', 'visible');
            }

            $.ajax({
                url: redcoWebP.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_webp_get_convertible_count',
                    nonce: redcoWebP.nonces.bulk_convert
                },
                success: (response) => {

                    if (response.success && response.data) {
                        const count = response.data.convertible_count || 0;
                        const hasImages = response.data.has_convertible_images || false;

                        // Update button states
                        if (hasImages && count > 0) {
                            // Enable buttons
                            $headerButton.prop('disabled', false);
                            $sidebarButton.prop('disabled', false);

                            // Update button text
                            if ($buttonText.length) {
                                $buttonText.text(`Convert ${count} images`);
                            }

                            // Convert buttons enabled
                        } else {
                            // Disable buttons
                            $headerButton.prop('disabled', true);
                            $sidebarButton.prop('disabled', true);

                            // Update button text
                            if ($buttonText.length) {
                                $buttonText.text('No images to convert');
                            }

                            // Convert buttons disabled - no convertible images
                        }

                        // Hide spinner
                        if ($buttonSpinner.length) {
                            $buttonSpinner.css('visibility', 'hidden');
                        }

                        // Update status message if container exists
                        const $statusContainer = $('#conversion-status-message');
                        const $statusText = $('#status-message-text');
                        if ($statusContainer.length && $statusText.length) {
                            if (hasImages && count > 0) {
                                $statusText.text(`${count} images ready for WebP conversion`);
                                $statusContainer.show();
                            } else {
                                $statusText.text('All images have been converted to WebP format');
                                $statusContainer.show();
                            }
                        }

                    } else {
                        this.handleButtonStateError();
                    }
                },
                error: (xhr, status, error) => {
                    this.handleButtonStateError();
                }
            });
        },

        // Handle button state errors
        handleButtonStateError: function() {
            const $button = $('#bulk-convert-images');
            const $buttonText = $('#convert-button-text');
            const $statusMessage = $('#conversion-status-message');
            const $statusText = $('#status-message-text');

            $button.prop('disabled', true).hide();
            $buttonText.text('Error Loading');
            $statusMessage.show();
            $statusText.text('Unable to check convertible images');
            $statusMessage.css('border-left-color', '#dc3545');
        },





        // CRITICAL FIX: Scan images functionality
        scanImages: function() {
            console.log('🔍 Scanning images for WebP conversion...');

            // Check if redcoWebP is available
            if (typeof redcoWebP === 'undefined') {
                console.error('❌ redcoWebP not available for image scanning');
                this.showToast('Error: WebP system not initialized', 'error');
                return;
            }

            const $button = $('#scan-images');
            const originalText = $button.text();

            // Show loading state
            $button.prop('disabled', true).html('<span class="dashicons dashicons-search spin"></span> Scanning...');

            $.ajax({
                url: redcoWebP.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_webp_scan_images'
                },
                success: (response) => {
                    console.log('✅ Image scan response:', response);

                    if (response.success) {
                        const data = response.data;

                        // Update analysis results display
                        this.updateAnalysisResults(data);

                        // CRITICAL FIX: Show convert button if convertible images found
                        this.updateConvertButtonState(data);

                        // Show detailed results
                        this.showScanResults(data);

                        this.showToast(`Scan complete: ${data.unconverted_images} images ready for conversion`, 'success');
                    } else {
                        console.error('❌ Image scan failed:', response);
                        this.showToast('Failed to scan images: ' + (response.data || 'Unknown error'), 'error');
                    }
                },
                error: (xhr, status, error) => {
                    console.error('❌ AJAX error scanning images:', error);
                    this.showToast('Network error scanning images: ' + error, 'error');
                },
                complete: () => {
                    // Restore button state
                    $button.prop('disabled', false).text(originalText);
                }
            });
        },

        // CRITICAL FIX: Analyze WebP potential functionality
        analyzeWebPPotential: function() {
            console.log('📊 Analyzing WebP conversion potential...');

            // Check if redcoWebP is available
            if (typeof redcoWebP === 'undefined') {
                console.error('❌ redcoWebP not available for WebP analysis');
                this.showToast('Error: WebP system not initialized', 'error');
                return;
            }

            const $button = $('#analyze-webp-potential');
            const originalText = $button.text();

            // Show loading state
            $button.prop('disabled', true).html('<span class="dashicons dashicons-analytics spin"></span> Analyzing...');

            $.ajax({
                url: redcoWebP.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_webp_analyze_webp_potential'
                },
                success: (response) => {
                    console.log('✅ WebP analysis response:', response);

                    if (response.success) {
                        const data = response.data;

                        // Show analysis modal with detailed results
                        this.showAnalysisModal(data);

                        this.showToast(`Analysis complete: ${data.processable_count} images analyzed`, 'success');
                    } else {
                        console.error('❌ WebP analysis failed:', response);
                        this.showToast('Failed to analyze WebP potential: ' + (response.data || 'Unknown error'), 'error');
                    }
                },
                error: (xhr, status, error) => {
                    console.error('❌ AJAX error analyzing WebP potential:', error);
                    this.showToast('Network error analyzing WebP potential: ' + error, 'error');
                },
                complete: () => {
                    // Restore button state
                    $button.prop('disabled', false).text(originalText);
                }
            });
        },

        // CRITICAL FIX: Test WebP support functionality
        testWebPSupport: function() {
            console.log('🧪 Testing WebP server support...');

            // Check if redcoWebP is available
            if (typeof redcoWebP === 'undefined') {
                console.error('❌ redcoWebP not available for WebP testing');
                this.showToast('Error: WebP system not initialized', 'error');
                return;
            }

            const $button = $('#test-webp-support');
            const originalText = $button.text();

            // Show loading state
            $button.prop('disabled', true).html('<span class="dashicons dashicons-admin-tools spin"></span> Testing...');

            $.ajax({
                url: redcoWebP.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_webp_test_conversion',
                    nonce: redcoWebP.nonces.test
                },
                success: (response) => {
                    console.log('✅ WebP test response:', response);

                    if (response.success) {
                        this.showToast('WebP support test completed successfully', 'success');

                        // Show test results modal
                        this.showTestResultsModal(response.data);
                    } else {
                        console.error('❌ WebP test failed:', response);
                        this.showToast('WebP support test failed: ' + (response.data || 'Unknown error'), 'error');
                    }
                },
                error: (xhr, status, error) => {
                    console.error('❌ AJAX error testing WebP support:', error);
                    this.showToast('Network error testing WebP support: ' + error, 'error');
                },
                complete: () => {
                    // Restore button state
                    $button.prop('disabled', false).text(originalText);
                }
            });
        },

        // CRITICAL FIX: Update convert button state based on scan results
        updateConvertButtonStateWithData: function(data) {

            const $convertButton = $('#bulk-convert-images');
            const $convertButtonText = $('#convert-button-text');
            const $statusMessage = $('#conversion-status-message');
            const $statusText = $('#status-message-text');

            if (data.unconverted_images && data.unconverted_images > 0) {
                // Show convert button for convertible images
                $convertButton.show();
                $convertButton.prop('disabled', false);
                $convertButtonText.text(`Convert ${data.unconverted_images} Images`);

                // Show status message
                $statusMessage.show();
                $statusText.text(`${data.unconverted_images} images ready for WebP conversion`);
                $statusMessage.css('border-left-color', '#4CAF50');
                $statusMessage.find('.dashicons').removeClass('dashicons-warning').addClass('dashicons-yes-alt');

                // Convert button enabled
            } else {
                // Hide convert button if no convertible images
                $convertButton.hide();

                // Show status message
                $statusMessage.show();
                $statusText.text('All images have been converted to WebP format');
                $statusMessage.css('border-left-color', '#666');
                $statusMessage.find('.dashicons').removeClass('dashicons-yes-alt').addClass('dashicons-info');

                // Convert button hidden - no convertible images
            }
        },

        // CRITICAL FIX: Update analysis results display
        updateAnalysisResults: function(data) {
            const $analysisResults = $('#analysis-results');
            const $analysisItem = $analysisResults.find('.analysis-item').first();

            if ($analysisItem.length > 0) {
                $analysisItem.html(`
                    <strong>${data.unconverted_images || 0}</strong>
                    images ready for WebP conversion
                `);
            }
        },

        // CRITICAL FIX: Show scan results modal/alert
        showScanResults: function(data) {
            let message = `Image Scan Complete!\n\n`;
            message += `Total Images: ${data.total_images || 0}\n`;
            message += `Already Converted: ${data.converted_images || 0}\n`;
            message += `Ready for Conversion: ${data.unconverted_images || 0}\n`;
            message += `Estimated Savings: ${data.estimated_savings_percent || 30}%`;

            // Scan results processed

            // Optional: Show alert for immediate feedback
            // alert(message);
        },

        // Enhanced WebP Test Function
        testEnhancedWebP: function() {
            console.log('🚀 Starting Enhanced WebP Test...');

            const $button = $('#test-enhanced-webp');
            const originalText = $button.text();
            $button.prop('disabled', true).text('Testing...');

            $.ajax({
                url: redcoWebP.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_webp_enhanced_test',
                    nonce: redcoWebP.nonces.test
                },
                success: function(response) {
                    $button.prop('disabled', false).text(originalText);
                    console.log('Enhanced WebP Test Results:', response);

                    if (response.success) {
                        const data = response.data;

                        console.log('=== ENHANCED SERVER CAPABILITIES ===');
                        console.log('WebP Support:', data.capabilities.webp_support ? '✅ YES' : '❌ NO');
                        console.log('Memory Available:', data.capabilities.memory_available ? '✅ YES' : '❌ NO');
                        console.log('Write Permissions:', data.capabilities.write_permissions ? '✅ YES' : '❌ NO');
                        console.log('GD Info:', data.capabilities.gd_info);

                        if (data.test_conversion) {
                            console.log('');
                            console.log('=== ENHANCED TEST CONVERSION ===');
                            if (data.test_conversion.success) {
                                console.log('✅ Enhanced test conversion SUCCESSFUL!');
                                console.log('Image ID:', data.test_conversion.image_id);
                                console.log('Original File:', data.test_conversion.original_file);
                                console.log('WebP File:', data.test_conversion.webp_file);
                                console.log('Original Size:', data.test_conversion.original_size, 'bytes');
                                console.log('WebP Size:', data.test_conversion.webp_size, 'bytes');
                                console.log('Savings:', data.test_conversion.original_size - data.test_conversion.webp_size, 'bytes');

                                alert('✅ Enhanced WebP Test PASSED!\n\n' +
                                      'Server supports WebP conversion with enhanced features.\n' +
                                      'Test conversion successful!\n\n' +
                                      'Check console for detailed results.');
                            } else {
                                console.log('❌ Enhanced test conversion failed:', data.test_conversion.error);
                                alert('❌ Enhanced WebP Test FAILED!\n\n' +
                                      'Test conversion failed: ' + data.test_conversion.error + '\n\n' +
                                      'Check console for detailed results.');
                            }
                        }

                        if (data.logs && data.logs.length > 0) {
                            console.log('');
                            console.log('=== ENHANCED LOGS ===');
                            data.logs.forEach(log => console.log(log));
                        }

                    } else {
                        console.log('❌ Enhanced WebP test failed:', response.data);
                        alert('❌ Enhanced WebP Test FAILED!\n\n' + response.data);
                    }
                },
                error: function(xhr, status, error) {
                    $button.prop('disabled', false).text(originalText);
                    console.log('❌ Enhanced WebP Test AJAX Error:', error);
                    alert('❌ Enhanced WebP Test Error!\n\nAJAX request failed: ' + error);
                }
            });
        }
    };

    // Initialize when document is ready
    $(document).ready(function() {
        // Multiple detection methods for better reliability
        const isWebPPage = window.location.href.indexOf('tab=smart-webp-conversion') !== -1 ||
                          $('.redco-module-tab[data-module="smart-webp-conversion"]').length > 0 ||
                          $('#test-webp-modal').length > 0;

        if (isWebPPage) {
            WebPAdmin.init();
        }
    });

    // REMOVED: Auto-refresh cleanup - no longer needed

    // Make WebPAdmin globally available
    window.WebPAdmin = WebPAdmin;

    // Fallback initialization - instant retry if needed
    if (typeof window.WebPAdmin !== 'undefined' && !window.WebPAdmin.initialized) {
        if ($('#test-webp-modal').length > 0) {
            WebPAdmin.init();
            WebPAdmin.initialized = true;
        }
    }

})(jQuery);

/**
 * Enhanced WebP Module Features
 * Dynamic button states, recent conversions, and modern UI
 */
(function($) {
    'use strict';

    window.RedcoWebPEnhanced = {
        initialized: false,
        currentOffset: 0,
        currentLimit: 10,
        currentSort: 'date',

        // Initialize enhanced features
        init: function() {
            if (this.initialized) return;



            this.initDynamicButtonState();
            this.initRecentConversions();
            this.initToastNotifications();
            this.bindEvents();

            // REMOVED: Duplicate stats call - WebPAdmin.init() already handles this

            this.initialized = true;
        },

        // Initialize dynamic button state management
        initDynamicButtonState: function() {
            this.checkConvertibleImages();
        },

        // Check for convertible images and update button state
        checkConvertibleImages: function() {
            const self = this;



            $.ajax({
                url: redcoWebP.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_webp_get_convertible_count',
                    nonce: redcoWebP.nonces.bulk_convert
                },
                success: function(response) {
                    if (response.success) {
                        self.updateButtonState(response.data);
                    } else {
                        self.updateButtonState({
                            convertible_count: 0,
                            has_convertible_images: false,
                            message: 'Unable to check images'
                        });
                    }
                },
                error: function(xhr, status, error) {
                    self.updateButtonState({
                        convertible_count: 0,
                        has_convertible_images: false,
                        message: 'Connection error'
                    });
                }
            });
        },

        // Update button state based on convertible images
        updateButtonState: function(data) {
            const $button = $('#bulk-convert-images');
            const $headerButton = $('#header-bulk-convert-images');
            const $buttonText = $('#convert-button-text');
            const $spinner = $('#convert-button-spinner');
            const $statusMessage = $('#conversion-status-message');
            const $statusText = $('#status-message-text');

            // Hide spinner
            $spinner.hide();



            if (data.has_convertible_images) {
                // Enable main button
                $button.prop('disabled', false)
                       .removeClass('button-secondary testing-mode')
                       .addClass('button-primary')
                       .css('background', '')
                       .css('border-color', '');

                // Enable header button
                $headerButton.prop('disabled', false);

                $buttonText.text(`Convert ${data.convertible_count} Images`);

                // Show status message
                $statusMessage.show();
                $statusText.text(data.message);
                $statusMessage.css('border-left-color', '#4CAF50');
                $statusMessage.find('.dashicons').removeClass('dashicons-info').addClass('dashicons-yes-alt');

            } else {
                // Disable main button
                $button.prop('disabled', true)
                       .removeClass('button-primary testing-mode')
                       .addClass('button-secondary')
                       .css('background', '')
                       .css('border-color', '');

                // Disable header button
                $headerButton.prop('disabled', true);

                $buttonText.text('No Images to Convert');

                // Show status message
                $statusMessage.show();
                $statusText.text(data.message || 'All images have been converted to WebP format');
                $statusMessage.css('border-left-color', '#666');
                $statusMessage.find('.dashicons').removeClass('dashicons-yes-alt').addClass('dashicons-info');
            }


        },

        // Initialize recent conversions functionality
        initRecentConversions: function() {
            this.loadRecentConversions();
        },

        // CLEAN OVERHAUL: Load recent conversions via AJAX (max 10, no pagination)
        loadRecentConversions: function() {
            const self = this;

            $('#conversions-loading').show();
            $('#recent-conversions-list').hide();
            $('#conversions-empty').hide();

            $.ajax({
                url: redcoWebP.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_webp_get_recent_conversions',
                    limit: 10, // CLEAN OVERHAUL: Always limit to 10
                    offset: 0, // CLEAN OVERHAUL: Always start from 0
                    sort_by: this.currentSort,
                    nonce: redcoWebP.nonces.stats // CRITICAL FIX: Use stats nonce for recent conversions
                },
                success: function(response) {
                    $('#conversions-loading').hide();

                    if (response.success) {
                        self.renderConversions(response.data); // CLEAN OVERHAUL: No append parameter
                    } else {
                        $('#conversions-empty').show();
                    }
                },
                error: function(xhr, status, error) {
                    $('#conversions-loading').hide();
                    $('#conversions-empty').show();
                    console.error('❌ Enhanced recent conversions AJAX error:', {
                        status: xhr.status,
                        statusText: xhr.statusText,
                        responseText: xhr.responseText,
                        error: error
                    });
                }
            });
        },

        // CLEAN OVERHAUL: Render conversions list (max 10, no pagination)
        renderConversions: function(data) {
            const $list = $('#recent-conversions-list');

            $list.empty(); // Always clear the list

            if (!data.conversions || data.conversions.length === 0) {
                $('#conversions-empty').show();
                return;
            }

            $list.show();
            $('#conversions-empty').hide();

            // CLEAN OVERHAUL: Render only first 10 conversions, no pagination
            const conversionsToShow = data.conversions.slice(0, 10);
            conversionsToShow.forEach(conversion => {
                const conversionHtml = this.renderConversionItem(conversion);
                $list.append(conversionHtml);
            });

            // CLEAN OVERHAUL: Hide all pagination elements
            $('#conversions-load-more').hide();
            $('#conversions-pagination-info').hide();


        },

        // CLEAN OVERHAUL: Render individual conversion item
        renderConversionItem: function(conversion) {
            return `
                <div class="recent-conversion-item">
                    <div class="conversion-main">
                        <div class="conversion-title">${conversion.title}</div>
                        <div class="conversion-meta">
                            <span class="original-size">${conversion.formatted_original_size}</span>
                            <span class="arrow">→</span>
                            <span class="webp-size">${conversion.formatted_webp_size}</span>
                            <span class="savings">${conversion.savings_percentage}% saved</span>
                            <span class="date">${conversion.formatted_date}</span>
                        </div>
                    </div>
                </div>
            `;
        },

        // Initialize toast notifications - REMOVED: Now uses global toast system
        initToastNotifications: function() {
            // WebP module now uses the global toast notification system
            // No custom toast initialization needed
        },

        // REMOVED: Auto-save interception - WebP module now uses global toast system

        // REMOVED: Global auto-save override - WebP module now uses standard global toast system

        // Bind events for enhanced features
        bindEvents: function() {
            const self = this;

            // Conversions sorting
            $('#conversions-sort').on('change', function() {
                self.currentSort = $(this).val();
                self.loadRecentConversions(); // CLEAN OVERHAUL: No offset needed
            });

            // CLEAN OVERHAUL: Removed Load More button handler (no longer needed)

            // REMOVED: Manual refresh stats button (no longer needed with simple stats)

            // Refresh convertible count after conversion
            $(document).on('webp-conversion-completed', function() {
                setTimeout(() => {
                    self.checkConvertibleImages();
                    self.loadRecentConversions();

                    // STATS FIX: Also refresh stats after conversion
                    if (typeof WebPAdmin !== 'undefined' && typeof WebPAdmin.refreshStats === 'function') {
                        WebPAdmin.refreshStats();
                    }
                }, 1000);
            });

            // REMOVED: Auto-convert checkbox should NOT trigger button state checks
            // The auto-convert setting is purely for new uploads, not existing images

            // Refresh button state periodically
            setInterval(() => {
                if ($('#bulk-convert-images').is(':visible')) {
                    self.checkConvertibleImages();
                }
            }, 30000); // Every 30 seconds



        },

        // Show toast notification using global toast system
        showToast: function(message, type, duration) {
            if (typeof showToast === 'function') {
                showToast(message, type, duration || 5000);
            }
        }

        // REMOVED: showConversionDetails function (no longer needed)
    };

    // Initialize enhanced features when document is ready
    $(document).ready(function() {
        const isWebPPage = window.location.href.indexOf('tab=smart-webp-conversion') !== -1 ||
                          $('.redco-module-tab[data-module="smart-webp-conversion"]').length > 0 ||
                          $('#test-webp-modal').length > 0;

        if (isWebPPage) {
            RedcoWebPEnhanced.init();
        }
    });

})(jQuery);

